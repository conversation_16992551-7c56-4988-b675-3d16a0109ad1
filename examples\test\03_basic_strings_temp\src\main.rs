use std::io::{self, Write};


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Basic String Test");
    println!("{}", "=========================================");
    let mut str1 = "Hello";
    let mut str2 = "World";
    let mut str3 = "";
    let mut str4 = " ";
    let mut str5 = "123";
    println!("{}", "Basic String Variables:");
    println!("{}", format!("{}{}" , "str1 = ", str1));
    println!("{}", format!("{}{}" , "str2 = ", str2));
    println!("{}", format!("{}{}" , format!("{}{}" , "str3 = '", str3), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , "str4 = '", str4), "'"));
    println!("{}", format!("{}{}" , "str5 = ", str5));
    println!("{}", "");
    println!("{}", "String Concatenation:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "Simple: ", str1), " "), str2));
    println!("{}", (format!("{}{}" , format!("{}{}" , "Empty: ", str1), str3) + str2));
    println!("{}", (format!("{}{}" , format!("{}{}" , "Space: ", str1), str4) + str2));
    println!("{}", format!("{}{}" , format!("{}{}" , "Numbers: ", str5), " test"));
    println!("{}", "");
    let mut num1 = 42;
    let mut num2 = 3;
    let mut bool1 = true;
    let mut bool2 = false;
    println!("{}", "String with Other Types:");
    println!("{}", format!("{}{}" , "Number: ", num1));
    println!("{}", format!("{}{}" , "Another number: ", num2));
    println!("{}", format!("{}{}" , "Boolean true: ", bool1));
    println!("{}", format!("{}{}" , "Boolean false: ", bool2));
    println!("{}", format!("{}{}" , "Null value: ", "null"));
    println!("{}", "");
    println!("{}", "Complex Concatenations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Values: ", num1), ", "), num2), ", "), bool1));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Math: ", num1), " + "), num2), " = "), (num1 + num2)));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Comparison: ", num1), " > "), num2), " is "), (num1 > num2)));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Text and numbers: ", str1), " "), num1), " "), str2));
    println!("{}", "");
    println!("{}", "Empty and Space Handling:");
    println!("{}", format!("{}{}" , format!("{}{}" , "Before", str3), "After"));
    println!("{}", format!("{}{}" , format!("{}{}" , "Before", str4), "After"));
    println!("{}", format!("{}{}" , format!("{}{}" , "[", str3), "]"));
    println!("{}", format!("{}{}" , format!("{}{}" , "[", str4), "]"));
    println!("{}", "");
    let mut long_str = "This is a longer string for testing the string handling capabilities";
    println!("{}", "Long String:");
    println!("{}", long_str);
    println!("{}", format!("{}{}" , "Prefix: ", long_str));
    println!("{}", format!("{}{}" , long_str, " :Suffix"));
    println!("{}", "");
    let mut special1 = "Test!@#$%";
    let mut special2 = "Numbers123";
    let mut special3 = "Spaces   Here";
    let mut special4 = "Symbols()[]{}+-*/";
    println!("{}", "Special Characters:");
    println!("{}", special1);
    println!("{}", special2);
    println!("{}", special3);
    println!("{}", special4);
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "Combined: ", special1), " "), special2));
    println!("{}", "");
    println!("{}", "Multiple Data Types:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "All types: ", str1), ", "), num1), ", "), 3.14f32), ", "), bool1), ", "), "null"));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Expression: ", str1), " has "), (5 * 2)), " letters times "), 2.5f32));
    println!("{}", "");
    println!("{}", "Repeated Patterns:");
    println!("{}", format!("{}{}" , "Single: ", str1));
    println!("{}", format!("{}{}" , format!("{}{}" , "Double: ", str1), str1));
    println!("{}", (format!("{}{}" , format!("{}{}" , "Triple: ", str1), str1) + str1));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "With spaces: ", str1), " "), str1), " "), str1));
    println!("{}", "");
    println!("{}", "Edge Cases:");
    println!("{}", format!("{}{}" , str3, "Empty start"));
    println!("{}", format!("{}{}" , "Empty end", str3));
    println!("{}", format!("{}{}" , (str3 + str3), "Double empty"));
    println!("{}", format!("{}{}" , ((str4 + str4) + str4), "Triple space"));
    println!("{}", "=========================================");
    println!("{}", "Basic String Test Complete");
    println!("{}", "=========================================");
}

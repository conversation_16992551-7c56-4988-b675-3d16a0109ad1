use std::io::{self, Write};


fn main() {
    println!("{}", "Testing Array Indexing...");
    let mut numbers = vec![10, 20, 30, 40, 50];
    println!("{}", "Array created: [10, 20, 30, 40, 50]");
    println!("{}", "Test 2: Array indexing");
    let mut first = numbers[0 as usize];
    println!("{}", format!("{}{}" , "First element: ", first));
    let mut second = numbers[1 as usize];
    println!("{}", format!("{}{}" , "Second element: ", second));
    let mut last = numbers[4 as usize];
    println!("{}", format!("{}{}" , "Last element: ", last));
    println!("{}", "Test 3: Array modification");
    numbers[0 as usize] = 100;
    println!("{}", "Modified first element to 100");
    println!("{}", format!("{}{}" , "New first element: ", numbers[0 as usize]));
    let mut names = vec!["<PERSON>", "<PERSON>", "<PERSON>"];
    println!("{}", "String array created");
    let mut first_name = names[0 as usize];
    println!("{}", format!("{}{}" , "First name: ", first_name));
    println!("{}", "Array indexing test complete!");
}

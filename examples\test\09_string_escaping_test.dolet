# String Escaping and Character Literals Test
# Testing the fixes for string escaping and character support

say "========================================="
say "String Escaping and Character Literals Test"
say "========================================="

# Basic string escaping
set escaped_quote = "She said \"Hello World\""
set escaped_backslash = "Path: C:\\Users\\<USER>\nLine 2"
set escaped_tab = "Column1\tColumn2"

say "String Escaping Tests:"
say escaped_quote
say escaped_backslash
say escaped_newline
say escaped_tab
say ""

# Character literals
set char_a = 'A'
set char_digit = '5'
set char_symbol = '@'
set char_space = ' '

say "Character Literal Tests:"
say "char_a = " + char_a
say "char_digit = " + char_digit
say "char_symbol = " + char_symbol
say "char_space = '" + char_space + "'"
say ""

# Escaped character literals
set char_newline = '\n'
set char_tab = '\t'
set char_backslash = '\\'
set char_quote = '\''

say "Escaped Character Literals:"
say "char_newline = " + char_newline
say "char_tab = " + char_tab
say "char_backslash = " + char_backslash
say "char_quote = " + char_quote
say ""

# Mixed string and character operations
say "Mixed Operations:"
say "Character in string: " + char_a + " is a letter"
say "Multiple chars: " + char_a + char_digit + char_symbol
say "Escaped quote in context: " + escaped_quote + " - complete"
say ""

# Complex escaping
set complex_string = "Text with \"quotes\" and \\backslashes\\ and \ttabs\t"
say "Complex Escaping:"
say complex_string
say ""

# String with various escape sequences
set all_escapes = "Newline:\nTab:\tQuote:\"Backslash:\\Null:\0End"
say "All Escape Sequences:"
say all_escapes

say "========================================="
say "String Escaping Test Complete"
say "========================================="

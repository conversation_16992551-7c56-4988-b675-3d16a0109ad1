# Comprehensive Data Types Test
# Testing all supported data types and type inference

say "========================================="
say "Comprehensive Data Types Test"
say "========================================="

# Integer types - various sizes and ranges
set tiny_int = 1
set small_int = 42
set medium_int = 1000
set large_int = 999999
set huge_int = 9223372036854775807
set negative_tiny = -1
set negative_small = -42
set negative_large = -999999
set zero_int = 0

say "Integer Types:"
say "tiny_int = " + tiny_int
say "small_int = " + small_int
say "medium_int = " + medium_int
say "large_int = " + large_int
say "huge_int = " + huge_int
say "negative_tiny = " + negative_tiny
say "negative_small = " + negative_small
say "negative_large = " + negative_large
say "zero_int = " + zero_int
say ""

# Float types - testing precision boundaries
set simple_float = 3.14
set precise_float = 2.71828
set small_decimal = 0.1
set tiny_decimal = 0.00001
set negative_float = -3.14159
set zero_float = 0.0
set scientific_small = 1.23e-4
set scientific_large = 1.23e4

say "Float Types:"
say "simple_float = " + simple_float
say "precise_float = " + precise_float
say "small_decimal = " + small_decimal
say "tiny_decimal = " + tiny_decimal
say "negative_float = " + negative_float
say "zero_float = " + zero_float
say "scientific_small = " + scientific_small
say "scientific_large = " + scientific_large
say ""

# Double types - high precision
set pi_double = 3.141592653589793
set e_double = 2.718281828459045
set very_precise = 1.234567890123456
set negative_double = -2.718281828459045
set huge_double = 1.7976931348623157e308
set tiny_double = 2.2250738585072014e-308

say "Double Types:"
say "pi_double = " + pi_double
say "e_double = " + e_double
say "very_precise = " + very_precise
say "negative_double = " + negative_double
say "huge_double = " + huge_double
say "tiny_double = " + tiny_double
say ""

# Boolean types
set true_bool = true
set false_bool = false
set bool_from_comparison = 5 > 3
set bool_from_equality = 10 == 10
set bool_from_inequality = 7 != 8

say "Boolean Types:"
say "true_bool = " + true_bool
say "false_bool = " + false_bool
say "bool_from_comparison = " + bool_from_comparison
say "bool_from_equality = " + bool_from_equality
say "bool_from_inequality = " + bool_from_inequality
say ""

# String types - various formats
set simple_string = "Hello World"
set empty_string = ""
set string_with_numbers = "Test123"
set string_with_spaces = "   Spaces   "
set string_with_quotes = "She said \"Hello\""
set multiword_string = "This is a longer string with multiple words"
set special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"

say "String Types:"
say "simple_string = " + simple_string
say "empty_string = '" + empty_string + "'"
say "string_with_numbers = " + string_with_numbers
say "string_with_spaces = '" + string_with_spaces + "'"
say "string_with_quotes = " + string_with_quotes
say "multiword_string = " + multiword_string
say "special_chars = " + special_chars
say ""

# Null types
set null_value = null
set nullable_int = null
set nullable_string = null
set nullable_bool = null

say "Null Types:"
say "null_value = " + null_value
say "nullable_int = " + nullable_int
say "nullable_string = " + nullable_string
say "nullable_bool = " + nullable_bool
say ""

# Character types (if supported)
set single_char = 'A'
set digit_char = '5'
set special_char = '@'
set space_char = ' '

say "Character Types:"
say "single_char = " + single_char
say "digit_char = " + digit_char
say "special_char = " + special_char
say "space_char = '" + space_char + "'"
say ""

# Type mixing and concatenation
set mixed_concat = "Number: " + 42 + ", Float: " + 3.14 + ", Bool: " + true
set complex_expression = "Result: " + (10 + 5) + " is " + (10 > 5)

say "Type Mixing:"
say "mixed_concat = " + mixed_concat
say "complex_expression = " + complex_expression

say "========================================="
say "Data Types Test Complete"
say "========================================="

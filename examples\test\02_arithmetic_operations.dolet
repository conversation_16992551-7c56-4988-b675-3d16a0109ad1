# Comprehensive Arithmetic Operations Test
# Testing all mathematical operations and edge cases

say "========================================="
say "Arithmetic Operations Test"
say "========================================="

# Basic arithmetic with integers
set a = 10
set b = 3
set c = 0
set d = -5

say "Basic Integer Arithmetic:"
say "a = " + a + ", b = " + b + ", c = " + c + ", d = " + d
say ""

# Addition
set add_positive = a + b
set add_zero = a + c
set add_negative = a + d
set add_negatives = d + (-3)

say "Addition Operations:"
say a + " + " + b + " = " + add_positive
say a + " + " + c + " = " + add_zero
say a + " + " + d + " = " + add_negative
say d + " + (-3) = " + add_negatives
say ""

# Subtraction
set sub_positive = a - b
set sub_zero = a - c
set sub_negative = a - d
set sub_larger = b - a

say "Subtraction Operations:"
say a + " - " + b + " = " + sub_positive
say a + " - " + c + " = " + sub_zero
say a + " - " + d + " = " + sub_negative
say b + " - " + a + " = " + sub_larger
say ""

# Multiplication
set mul_positive = a * b
set mul_zero = a * c
set mul_negative = a * d
set mul_negatives = d * (-2)

say "Multiplication Operations:"
say a + " * " + b + " = " + mul_positive
say a + " * " + c + " = " + mul_zero
say a + " * " + d + " = " + mul_negative
say d + " * (-2) = " + mul_negatives
say ""

# Division (if supported)
set div_normal = a / b
set div_exact = 15 / 3
set div_negative = a / d
set div_by_negative = (-20) / d

say "Division Operations:"
say a + " / " + b + " = " + div_normal
say "15 / 3 = " + div_exact
say a + " / " + d + " = " + div_negative
say "(-20) / " + d + " = " + div_by_negative
say ""

# Modulo operations (if supported)
set mod_normal = a % b
set mod_exact = 15 % 3
set mod_larger = b % a
set mod_negative = a % d

say "Modulo Operations:"
say a + " % " + b + " = " + mod_normal
say "15 % 3 = " + mod_exact
say b + " % " + a + " = " + mod_larger
say a + " % " + d + " = " + mod_negative
say ""

# Float arithmetic
set fa = 10.5
set fb = 3.2
set fc = 0.0
set fd = -2.7

say "Float Arithmetic:"
say "fa = " + fa + ", fb = " + fb + ", fc = " + fc + ", fd = " + fd
say ""

set float_add = fa + fb
set float_sub = fa - fb
set float_mul = fa * fb
set float_div = fa / fb

say "Float Operations:"
say fa + " + " + fb + " = " + float_add
say fa + " - " + fb + " = " + float_sub
say fa + " * " + fb + " = " + float_mul
say fa + " / " + fb + " = " + float_div
say ""

# Mixed type arithmetic
set mixed_add = a + fa
set mixed_sub = fa - b
set mixed_mul = a * fb
set mixed_div = fa / b

say "Mixed Type Operations:"
say a + " + " + fa + " = " + mixed_add
say fa + " - " + b + " = " + mixed_sub
say a + " * " + fb + " = " + mixed_mul
say fa + " / " + b + " = " + mixed_div
say ""

# Complex expressions
set complex1 = (a + b) * (a - b)
set complex2 = a * b + c * d
set complex3 = (fa + fb) / (fa - fb)
set complex4 = a + b * c - d

say "Complex Expressions:"
say "(" + a + " + " + b + ") * (" + a + " - " + b + ") = " + complex1
say a + " * " + b + " + " + c + " * " + d + " = " + complex2
say "(" + fa + " + " + fb + ") / (" + fa + " - " + fb + ") = " + complex3
say a + " + " + b + " * " + c + " - " + d + " = " + complex4
say ""

# Edge cases
set large_num = 999999
set small_num = 0.00001
set edge_add = large_num + small_num
set edge_mul = large_num * small_num

say "Edge Cases:"
say "Large + Small: " + large_num + " + " + small_num + " = " + edge_add
say "Large * Small: " + large_num + " * " + small_num + " = " + edge_mul

say "========================================="
say "Arithmetic Operations Test Complete"
say "========================================="

use std::io::{self, Write};


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Arithmetic Operations Test");
    println!("{}", "=========================================");
    let mut a = 10;
    let mut b = 3;
    let mut c = 0;
    let mut d = (-5);
    println!("{}", "Basic Integer Arithmetic:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "a = ", a), ", b = "), b), ", c = "), c), ", d = "), d));
    println!("{}", "");
    let mut add_positive = (a + b);
    let mut add_zero = (a + c);
    let mut add_negative = (a + d);
    let mut add_negatives = (d + (-3));
    println!("{}", "Addition Operations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " + "), b), " = "), add_positive));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " + "), c), " = "), add_zero));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " + "), d), " = "), add_negative));
    println!("{}", format!("{}{}" , format!("{}{}" , d, " + (-3) = "), add_negatives));
    println!("{}", "");
    let mut sub_positive = (a - b);
    let mut sub_zero = (a - c);
    let mut sub_negative = (a - d);
    let mut sub_larger = (b - a);
    println!("{}", "Subtraction Operations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " - "), b), " = "), sub_positive));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " - "), c), " = "), sub_zero));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " - "), d), " = "), sub_negative));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , b, " - "), a), " = "), sub_larger));
    println!("{}", "");
    let mut mul_positive = (a * b);
    let mut mul_zero = (a * c);
    let mut mul_negative = (a * d);
    let mut mul_negatives = (d * (-2));
    println!("{}", "Multiplication Operations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " * "), b), " = "), mul_positive));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " * "), c), " = "), mul_zero));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " * "), d), " = "), mul_negative));
    println!("{}", format!("{}{}" , format!("{}{}" , d, " * (-2) = "), mul_negatives));
    println!("{}", "");
    let mut div_normal = (a / b);
    let mut div_exact = (15 / 3);
    let mut div_negative = (a / d);
    let mut div_by_negative = ((-20) / d);
    println!("{}", "Division Operations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " / "), b), " = "), div_normal));
    println!("{}", format!("{}{}" , "15 / 3 = ", div_exact));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " / "), d), " = "), div_negative));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "(-20) / ", d), " = "), div_by_negative));
    println!("{}", "");
    let mut mod_normal = (a % b);
    let mut mod_exact = (15 % 3);
    let mut mod_larger = (b % a);
    let mut mod_negative = (a % d);
    println!("{}", "Modulo Operations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " % "), b), " = "), mod_normal));
    println!("{}", format!("{}{}" , "15 % 3 = ", mod_exact));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , b, " % "), a), " = "), mod_larger));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " % "), d), " = "), mod_negative));
    println!("{}", "");
    let mut fa = 10.5f32;
    let mut fb = 3.2f32;
    let mut fc = 0f32;
    let mut fd = (-2.7f32);
    println!("{}", "Float Arithmetic:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "fa = ", fa), ", fb = "), fb), ", fc = "), fc), ", fd = "), fd));
    println!("{}", "");
    let mut float_add = (fa + fb);
    let mut float_sub = (fa - fb);
    let mut float_mul = (fa * fb);
    let mut float_div = (fa / fb);
    println!("{}", "Float Operations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , fa, " + "), fb), " = "), float_add));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , fa, " - "), fb), " = "), float_sub));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , fa, " * "), fb), " = "), float_mul));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , fa, " / "), fb), " = "), float_div));
    println!("{}", "");
    let mut mixed_add = (a + fa);
    let mut mixed_sub = (fa - b);
    let mut mixed_mul = (a * fb);
    let mut mixed_div = (fa / b);
    println!("{}", "Mixed Type Operations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " + "), fa), " = "), mixed_add));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , fa, " - "), b), " = "), mixed_sub));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " * "), fb), " = "), mixed_mul));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , fa, " / "), b), " = "), mixed_div));
    println!("{}", "");
    let mut complex1 = ((a + b) * (a - b));
    let mut complex2 = ((a * b) + (c * d));
    let mut complex3 = ((fa + fb) / (fa - fb));
    let mut complex4 = ((a + (b * c)) - d);
    println!("{}", "Complex Expressions:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "(", a), " + "), b), ") * ("), a), " - "), b), ") = "), complex1));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " * "), b), " + "), c), " * "), d), " = "), complex2));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "(", fa), " + "), fb), ") / ("), fa), " - "), fb), ") = "), complex3));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " + "), b), " * "), c), " - "), d), " = "), complex4));
    println!("{}", "");
    let mut large_num = 999999;
    let mut small_num = 0.00001f32;
    let mut edge_add = (large_num + small_num);
    let mut edge_mul = (large_num * small_num);
    println!("{}", "Edge Cases:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Large + Small: ", large_num), " + "), small_num), " = "), edge_add));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Large * Small: ", large_num), " * "), small_num), " = "), edge_mul));
    println!("{}", "=========================================");
    println!("{}", "Arithmetic Operations Test Complete");
    println!("{}", "=========================================");
}

use std::io::{self, Write};


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Simple Arithmetic Test");
    println!("{}", "=========================================");
    let mut a = 10;
    let mut b = 3;
    let mut c = 0;
    let mut d = (-5);
    println!("{}", "Integer Variables:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "a = ", a), ", b = "), b), ", c = "), c), ", d = "), d));
    println!("{}", "");
    let mut add1 = (a + b);
    let mut add2 = (a + c);
    let mut add3 = (a + d);
    let mut add4 = (d + (-3));
    println!("{}", "Addition Operations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " + "), b), " = "), add1));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " + "), c), " = "), add2));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " + "), d), " = "), add3));
    println!("{}", format!("{}{}" , format!("{}{}" , d, " + (-3) = "), add4));
    println!("{}", "");
    let mut sub1 = (a - b);
    let mut sub2 = (a - c);
    let mut sub3 = (a - d);
    let mut sub4 = (b - a);
    println!("{}", "Subtraction Operations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " - "), b), " = "), sub1));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " - "), c), " = "), sub2));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " - "), d), " = "), sub3));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , b, " - "), a), " = "), sub4));
    println!("{}", "");
    let mut mul1 = (a * b);
    let mut mul2 = (a * c);
    let mut mul3 = (a * d);
    let mut mul4 = (d * (-2));
    println!("{}", "Multiplication Operations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " * "), b), " = "), mul1));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " * "), c), " = "), mul2));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " * "), d), " = "), mul3));
    println!("{}", format!("{}{}" , format!("{}{}" , d, " * (-2) = "), mul4));
    println!("{}", "");
    let mut div1 = (a / b);
    let mut div2 = (15 / 3);
    let mut div3 = (a / d);
    let mut div4 = ((-20) / d);
    println!("{}", "Division Operations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " / "), b), " = "), div1));
    println!("{}", format!("{}{}" , "15 / 3 = ", div2));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " / "), d), " = "), div3));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "(-20) / ", d), " = "), div4));
    println!("{}", "");
    let mut mod1 = (a % b);
    let mut mod2 = (15 % 3);
    let mut mod3 = (b % a);
    let mut mod4 = (a % d);
    println!("{}", "Modulo Operations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " % "), b), " = "), mod1));
    println!("{}", format!("{}{}" , "15 % 3 = ", mod2));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , b, " % "), a), " = "), mod3));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " % "), d), " = "), mod4));
    println!("{}", "");
    let mut fa = 10.5f32;
    let mut fb = 3.2f32;
    let mut fc = 0f32;
    let mut fd = (-2.7f32);
    println!("{}", "Float Variables:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "fa = ", fa), ", fb = "), fb), ", fc = "), fc), ", fd = "), fd));
    println!("{}", "");
    let mut float_add = (fa + fb);
    let mut float_sub = (fa - fb);
    let mut float_mul = (fa * fb);
    let mut float_div = (fa / fb);
    println!("{}", "Float Operations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , fa, " + "), fb), " = "), float_add));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , fa, " - "), fb), " = "), float_sub));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , fa, " * "), fb), " = "), float_mul));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , fa, " / "), fb), " = "), float_div));
    println!("{}", "");
    let mut complex1 = ((a + b) * (a - b));
    let mut complex2 = ((a * b) + (c * d));
    let mut complex3 = ((fa + fb) / (fa - fb));
    let mut complex4 = ((a + (b * c)) - d);
    println!("{}", "Complex Expressions:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "(", a), " + "), b), ") * ("), a), " - "), b), ") = "), complex1));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " * "), b), " + "), c), " * "), d), " = "), complex2));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "(", fa), " + "), fb), ") / ("), fa), " - "), fb), ") = "), complex3));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , a, " + "), b), " * "), c), " - "), d), " = "), complex4));
    println!("{}", "");
    let mut large1 = 999999;
    let mut large2 = 1000000;
    let mut large_add = (large1 + large2);
    let mut large_mul = (large1 * 2);
    println!("{}", "Large Numbers:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Large addition: ", large1), " + "), large2), " = "), large_add));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "Large multiplication: ", large1), " * 2 = "), large_mul));
    println!("{}", "=========================================");
    println!("{}", "Simple Arithmetic Test Complete");
    println!("{}", "=========================================");
}

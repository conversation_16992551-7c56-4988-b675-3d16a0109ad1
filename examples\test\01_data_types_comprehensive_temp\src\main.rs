use std::io::{self, Write};


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Comprehensive Data Types Test");
    println!("{}", "=========================================");
    let mut tiny_int = 1;
    let mut small_int = 42;
    let mut medium_int = 1000;
    let mut large_int = 999999;
    let mut huge_int = 9223372036854775807;
    let mut negative_tiny = (-1);
    let mut negative_small = (-42);
    let mut negative_large = (-999999);
    let mut zero_int = 0;
    println!("{}", "Integer Types:");
    println!("{}", format!("{}{}" , "tiny_int = ", tiny_int));
    println!("{}", format!("{}{}" , "small_int = ", small_int));
    println!("{}", format!("{}{}" , "medium_int = ", medium_int));
    println!("{}", format!("{}{}" , "large_int = ", large_int));
    println!("{}", format!("{}{}" , "huge_int = ", huge_int));
    println!("{}", format!("{}{}" , "negative_tiny = ", negative_tiny));
    println!("{}", format!("{}{}" , "negative_small = ", negative_small));
    println!("{}", format!("{}{}" , "negative_large = ", negative_large));
    println!("{}", format!("{}{}" , "zero_int = ", zero_int));
    println!("{}", "");
    let mut simple_float = 3.14f32;
    let mut precise_float = 2.71828f32;
    let mut small_decimal = 0.1f32;
    let mut tiny_decimal = 0.00001f32;
    let mut negative_float = (-3.14159f32);
    let mut zero_float = 0f32;
    let mut scientific_small = 1.23f32;
    // Unsupported statement
    let mut scientific_large = 1.23f32;
    // Unsupported statement
    println!("{}", "Float Types:");
    println!("{}", format!("{}{}" , "simple_float = ", simple_float));
    println!("{}", format!("{}{}" , "precise_float = ", precise_float));
    println!("{}", format!("{}{}" , "small_decimal = ", small_decimal));
    println!("{}", format!("{}{}" , "tiny_decimal = ", tiny_decimal));
    println!("{}", format!("{}{}" , "negative_float = ", negative_float));
    println!("{}", format!("{}{}" , "zero_float = ", zero_float));
    println!("{}", format!("{}{}" , "scientific_small = ", scientific_small));
    println!("{}", format!("{}{}" , "scientific_large = ", scientific_large));
    println!("{}", "");
    let mut pi_double = 3.141592653589793f64;
    let mut e_double = 2.718281828459045f64;
    let mut very_precise = 1.234567890123456f64;
    let mut negative_double = (-2.718281828459045f64);
    let mut huge_double = 1.7976931348623157f64;
    // Unsupported statement
    let mut tiny_double = 2.2250738585072014f64;
    // Unsupported statement
    println!("{}", "Double Types:");
    println!("{}", format!("{}{}" , "pi_double = ", pi_double));
    println!("{}", format!("{}{}" , "e_double = ", e_double));
    println!("{}", format!("{}{}" , "very_precise = ", very_precise));
    println!("{}", format!("{}{}" , "negative_double = ", negative_double));
    println!("{}", format!("{}{}" , "huge_double = ", huge_double));
    println!("{}", format!("{}{}" , "tiny_double = ", tiny_double));
    println!("{}", "");
    let mut true_bool = true;
    let mut false_bool = false;
    let mut bool_from_comparison = (5 > 3);
    let mut bool_from_equality = (10 == 10);
    let mut bool_from_inequality = (7 != 8);
    println!("{}", "Boolean Types:");
    println!("{}", format!("{}{}" , "true_bool = ", true_bool));
    println!("{}", format!("{}{}" , "false_bool = ", false_bool));
    println!("{}", format!("{}{}" , "bool_from_comparison = ", bool_from_comparison));
    println!("{}", format!("{}{}" , "bool_from_equality = ", bool_from_equality));
    println!("{}", format!("{}{}" , "bool_from_inequality = ", bool_from_inequality));
    println!("{}", "");
    let mut simple_string = "Hello World";
    let mut empty_string = "";
    let mut string_with_numbers = "Test123";
    let mut string_with_spaces = "   Spaces   ";
    let mut string_with_quotes = "She said \\"Hello\\"";
    let mut multiword_string = "This is a longer string with multiple words";
    let mut special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?";
    println!("{}", "String Types:");
    println!("{}", format!("{}{}" , "simple_string = ", simple_string));
    println!("{}", format!("{}{}" , format!("{}{}" , "empty_string = '", empty_string), "'"));
    println!("{}", format!("{}{}" , "string_with_numbers = ", string_with_numbers));
    println!("{}", format!("{}{}" , format!("{}{}" , "string_with_spaces = '", string_with_spaces), "'"));
    println!("{}", format!("{}{}" , "string_with_quotes = ", string_with_quotes));
    println!("{}", format!("{}{}" , "multiword_string = ", multiword_string));
    println!("{}", format!("{}{}" , "special_chars = ", special_chars));
    println!("{}", "");
    let mut null_value = "null";
    let mut nullable_int = "null";
    let mut nullable_string = "null";
    let mut nullable_bool = "null";
    println!("{}", "Null Types:");
    println!("{}", format!("{}{}" , "null_value = ", null_value));
    println!("{}", format!("{}{}" , "nullable_int = ", nullable_int));
    println!("{}", format!("{}{}" , "nullable_string = ", nullable_string));
    println!("{}", format!("{}{}" , "nullable_bool = ", nullable_bool));
    println!("{}", "");
    let mut single_char = /* unsupported expr: Char('A') */;
    let mut digit_char = /* unsupported expr: Char('5') */;
    let mut special_char = /* unsupported expr: Char('@') */;
    let mut space_char = /* unsupported expr: Char(' ') */;
    println!("{}", "Character Types:");
    println!("{}", format!("{}{}" , "single_char = ", single_char));
    println!("{}", format!("{}{}" , "digit_char = ", digit_char));
    println!("{}", format!("{}{}" , "special_char = ", special_char));
    println!("{}", format!("{}{}" , format!("{}{}" , "space_char = '", space_char), "'"));
    println!("{}", "");
    let mut mixed_concat = format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Number: ", 42), ", Float: "), 3.14f32), ", Bool: "), true);
    let mut complex_expression = format!("{}{}" , format!("{}{}" , format!("{}{}" , "Result: ", (10 + 5)), " is "), (10 > 5));
    println!("{}", "Type Mixing:");
    println!("{}", format!("{}{}" , "mixed_concat = ", mixed_concat));
    println!("{}", format!("{}{}" , "complex_expression = ", complex_expression));
    println!("{}", "=========================================");
    println!("{}", "Data Types Test Complete");
    println!("{}", "=========================================");
}

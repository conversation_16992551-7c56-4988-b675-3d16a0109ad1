# Performance Test
# Testing computational performance and loops

say "========================================="
say "Performance Test"
say "========================================="

# Large number calculations
say "Large Number Calculations:"
set large1 = 999999
set large2 = 888888
set large3 = 777777

set large_sum = large1 + large2 + large3
set large_product = large1 * 2
set large_division = large1 / 3

say "Sum: " + large1 + " + " + large2 + " + " + large3 + " = " + large_sum
say "Product: " + large1 + " * 2 = " + large_product
say "Division: " + large1 + " / 3 = " + large_division
say ""

# Loop performance test
say "Loop Performance Test:"
set counter = 0
set sum = 0

while counter < 100:
    set sum = sum + counter
    set counter = counter + 1
end

say "Sum of numbers 0 to 99: " + sum
say ""

# Nested loop test
say "Nested Loop Test:"
set total = 0
for i from 1 to 10:
    for j from 1 to 10:
        set total = total + 1
    end
end
say "Total iterations (10x10): " + total
say ""

# Array processing performance
say "Array Processing Performance:"
set numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
set array_sum = 0
set i = 0

while i < 10:
    set array_sum = array_sum + numbers[i]
    set i = i + 1
end

say "Array sum: " + array_sum
say ""

# String processing performance
say "String Processing Performance:"
set base_string = "Performance"
set result_string = ""
set count = 0

while count < 10:
    set result_string = result_string + base_string + " "
    set count = count + 1
end

say "Concatenated string length test completed"
say ""

# Mathematical operations performance
say "Mathematical Operations Performance:"
set math_result = 0
set operations = 0

while operations < 50:
    set math_result = math_result + (operations * 2) + (operations / 2) - (operations % 3)
    set operations = operations + 1
end

say "Complex math operations result: " + math_result
say ""

# Function call performance
fun fibonacci_iterative(n: int) -> int:
    if n <= 1:
        return n
    end
    
    set a = 0
    set b = 1
    set i = 2
    
    while i <= n:
        set temp = a + b
        set a = b
        set b = temp
        set i = i + 1
    end
    
    return b
end

say "Function Call Performance:"
say "fibonacci_iterative(10) = " + fibonacci_iterative(10)
say "fibonacci_iterative(15) = " + fibonacci_iterative(15)
say "fibonacci_iterative(20) = " + fibonacci_iterative(20)
say ""

# Boolean operations performance
say "Boolean Operations Performance:"
set bool_operations = 0
set bool_result = true

while bool_operations < 100:
    set bool_result = bool_result and (bool_operations % 2 == 0)
    set bool_result = bool_result or (bool_operations % 3 == 0)
    set bool_operations = bool_operations + 1
end

say "Boolean operations completed, result: " + bool_result
say ""

# Array operations performance
say "Array Operations Performance:"
set perf_array = [10, 20, 30, 40, 50]
set array_operations = 0
set array_result = 0

while array_operations < 5:
    set array_result = array_result + perf_array[array_operations] * 2
    set array_operations = array_operations + 1
end

say "Array operations result: " + array_result
say ""

# Comparison operations performance
say "Comparison Operations Performance:"
set comparisons = 0
set comparison_results = 0

while comparisons < 100:
    if comparisons > 50:
        set comparison_results = comparison_results + 1
    end
    if comparisons % 10 == 0:
        set comparison_results = comparison_results + 2
    end
    set comparisons = comparisons + 1
end

say "Comparison operations result: " + comparison_results

say "========================================="
say "Performance Test Complete"
say "========================================="

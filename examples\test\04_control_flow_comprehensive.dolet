# Comprehensive Control Flow Test
# Testing if/else statements, loops, and complex conditions

say "========================================="
say "Control Flow Test"
say "========================================="

# Basic if/else statements
set age = 25
set score = 85
set is_student = true
set name = "Alice"

say "Basic If/Else Tests:"
say "age = " + age + ", score = " + score + ", is_student = " + is_student

if age >= 18:
    say "✓ Adult (age >= 18)"
else:
    say "✗ Minor (age < 18)"
end

if score >= 90:
    say "✓ Grade A (score >= 90)"
else:
    say "✗ Not Grade A (score < 90)"
end

if is_student:
    say "✓ Is a student"
else:
    say "✗ Not a student"
end
say ""

# Nested if/else statements
say "Nested If/Else Tests:"
if age >= 18:
    if score >= 80:
        if is_student:
            say "✓ Adult student with good grades"
        else:
            say "✓ Adult non-student with good grades"
        end
    else:
        say "✓ Adult with lower grades"
    end
else:
    if score >= 80:
        say "✓ Minor with good grades"
    else:
        say "✓ Minor with lower grades"
    end
end
say ""

# Multiple conditions with logical operators
say "Logical Operators Tests:"
set x = 10
set y = 5
set z = 15

if x > y and y < z:
    say "✓ x > y AND y < z: " + x + " > " + y + " AND " + y + " < " + z
end

if x > z or y < z:
    say "✓ x > z OR y < z: " + x + " > " + z + " OR " + y + " < " + z
end

if not (x < y):
    say "✓ NOT (x < y): NOT (" + x + " < " + y + ")"
end

if x > y and not (y > z):
    say "✓ Complex: x > y AND NOT (y > z)"
end
say ""

# Comparison operators
say "Comparison Operators Tests:"
set a = 10
set b = 10
set c = 20

if a == b:
    say "✓ Equal: " + a + " == " + b
end

if a != c:
    say "✓ Not equal: " + a + " != " + c
end

if a < c:
    say "✓ Less than: " + a + " < " + c
end

if c > a:
    say "✓ Greater than: " + c + " > " + a
end

if a <= b:
    say "✓ Less than or equal: " + a + " <= " + b
end

if c >= a:
    say "✓ Greater than or equal: " + c + " >= " + a
end
say ""

# String comparisons
say "String Comparison Tests:"
set str1 = "apple"
set str2 = "banana"
set str3 = "apple"

if str1 == str3:
    say "✓ String equality: '" + str1 + "' == '" + str3 + "'"
end

if str1 != str2:
    say "✓ String inequality: '" + str1 + "' != '" + str2 + "'"
end
say ""

# Boolean logic combinations
say "Complex Boolean Logic:"
set temp = 75
set humidity = 60
set is_sunny = true
set is_weekend = false

if temp > 70 and humidity < 70 and is_sunny:
    say "✓ Perfect weather conditions"
end

if (temp > 80 or is_sunny) and not is_weekend:
    say "✓ Good weather on weekday"
end

if temp > 60 and (is_sunny or humidity < 50):
    say "✓ Acceptable outdoor conditions"
end
say ""

# While loops (if supported)
say "While Loop Tests:"
set counter = 1
while counter <= 5:
    say "Counter: " + counter
    set counter = counter + 1
end
say ""

# For loops (if supported)
say "For Loop Tests:"
for i from 1 to 5:
    say "For loop iteration: " + i
end
say ""

# Nested loops
say "Nested Loop Tests:"
for i from 1 to 3:
    for j from 1 to 3:
        say "Nested: i=" + i + ", j=" + j
    end
end
say ""

# Loop with conditions
say "Loop with Conditions:"
set sum = 0
for i from 1 to 10:
    if i % 2 == 0:
        set sum = sum + i
        say "Adding even number: " + i + ", sum = " + sum
    end
end
say "Final sum of even numbers: " + sum
say ""

# Complex control flow
say "Complex Control Flow:"
set grade = 87
set attendance = 95
set participation = true

if grade >= 90:
    say "Grade: A"
else:
    if grade >= 80:
        if attendance >= 90 and participation:
            say "Grade: B+ (good attendance and participation)"
        else:
            say "Grade: B"
        end
    else:
        if grade >= 70:
            say "Grade: C"
        else:
            say "Grade: D or F"
        end
    end
end

say "========================================="
say "Control Flow Test Complete"
say "========================================="

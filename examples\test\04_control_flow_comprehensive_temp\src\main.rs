use std::io::{self, Write};


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Control Flow Test");
    println!("{}", "=========================================");
    let mut age = 25;
    let mut score = 85;
    let mut is_student = true;
    let mut name = "Alice";
    println!("{}", "Basic If/Else Tests:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "age = ", age), ", score = "), score), ", is_student = "), is_student));
    if (age >= 18) {
        println!("{}", "✓ Adult (age >= 18)");
    } else {
        println!("{}", "✗ Minor (age < 18)");
    }
    if (score >= 90) {
        println!("{}", "✓ Grade A (score >= 90)");
    } else {
        println!("{}", "✗ Not Grade A (score < 90)");
    }
    if is_student {
        println!("{}", "✓ Is a student");
    } else {
        println!("{}", "✗ Not a student");
    }
    println!("{}", "");
    println!("{}", "Nested If/Else Tests:");
    if (age >= 18) {
        if (score >= 80) {
            // Unsupported nested statement
        } else {
            println!("{}", "✓ Adult with lower grades");
        }
    } else {
        if (score >= 80) {
            println!("{}", "✓ Minor with good grades");
        } else {
            println!("{}", "✓ Minor with lower grades");
        }
    }
    println!("{}", "");
    println!("{}", "Logical Operators Tests:");
    let mut x = 10;
    let mut y = 5;
    let mut z = 15;
    if ((x > y) && (y < z)) {
        println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "✓ x > y AND y < z: ", x), " > "), y), " AND "), y), " < "), z));
    }
    if ((x > z) || (y < z)) {
        println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "✓ x > z OR y < z: ", x), " > "), z), " OR "), y), " < "), z));
    }
    if (!(x < y)) {
        println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "✓ NOT (x < y): NOT (", x), " < "), y), ")"));
    }
    if ((x > y) && (!(y > z))) {
        println!("{}", "✓ Complex: x > y AND NOT (y > z)");
    }
    println!("{}", "");
    println!("{}", "Comparison Operators Tests:");
    let mut a = 10;
    let mut b = 10;
    let mut c = 20;
    if (a == b) {
        println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "✓ Equal: ", a), " == "), b));
    }
    if (a != c) {
        println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "✓ Not equal: ", a), " != "), c));
    }
    if (a < c) {
        println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "✓ Less than: ", a), " < "), c));
    }
    if (c > a) {
        println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "✓ Greater than: ", c), " > "), a));
    }
    if (a <= b) {
        println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "✓ Less than or equal: ", a), " <= "), b));
    }
    if (c >= a) {
        println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "✓ Greater than or equal: ", c), " >= "), a));
    }
    println!("{}", "");
    println!("{}", "String Comparison Tests:");
    let mut str1 = "apple";
    let mut str2 = "banana";
    let mut str3 = "apple";
    if (str1 == str3) {
        println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "✓ String equality: '", str1), "' == '"), str3), "'"));
    }
    if (str1 != str2) {
        println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "✓ String inequality: '", str1), "' != '"), str2), "'"));
    }
    println!("{}", "");
    println!("{}", "Complex Boolean Logic:");
    let mut temp = 75;
    let mut humidity = 60;
    let mut is_sunny = true;
    let mut is_weekend = false;
    if (((temp > 70) && (humidity < 70)) && is_sunny) {
        println!("{}", "✓ Perfect weather conditions");
    }
    if (((temp > 80) || is_sunny) && (!is_weekend)) {
        println!("{}", "✓ Good weather on weekday");
    }
    if ((temp > 60) && (is_sunny || (humidity < 50))) {
        println!("{}", "✓ Acceptable outdoor conditions");
    }
    println!("{}", "");
    println!("{}", "While Loop Tests:");
    let mut counter = 1;
    while (counter <= 5) {
        println!("{}", format!("{}{}" , "Counter: ", counter));
        counter = (counter + 1);
    }
    println!("{}", "");
    println!("{}", "For Loop Tests:");
    for i in 1..=5 {
        println!("{}", format!("{}{}" , "For loop iteration: ", i));
    }
    println!("{}", "");
    println!("{}", "Nested Loop Tests:");
    for i in 1..=3 {
        // Unsupported statement in for loop
    }
    println!("{}", "");
    println!("{}", "Loop with Conditions:");
    let mut sum = 0;
    for i in 1..=10 {
        // Unsupported statement in for loop
    }
    println!("{}", format!("{}{}" , "Final sum of even numbers: ", sum));
    println!("{}", "");
    println!("{}", "Complex Control Flow:");
    let mut grade = 87;
    let mut attendance = 95;
    let mut participation = true;
    if (grade >= 90) {
        println!("{}", "Grade: A");
    } else {
        if (grade >= 80) {
            // Unsupported nested statement
        } else {
            // Unsupported nested statement
        }
    }
    println!("{}", "=========================================");
    println!("{}", "Control Flow Test Complete");
    println!("{}", "=========================================");
}

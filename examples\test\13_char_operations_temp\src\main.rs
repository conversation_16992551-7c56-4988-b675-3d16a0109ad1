use std::io::{self, Write};


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Character Operations Test");
    println!("{}", "=========================================");
    let mut char_a = 'A';
    let mut char_digit = '5';
    let mut char_symbol = '@';
    let mut char_space = ' ';
    println!("{}", "Basic Character Literals:");
    println!("{}", format!("{}{}" , "char_a = ", char_a));
    println!("{}", format!("{}{}" , "char_digit = ", char_digit));
    println!("{}", format!("{}{}" , "char_symbol = ", char_symbol));
    println!("{}", format!("{}{}" , format!("{}{}" , "char_space = '", char_space), "'"));
    println!("{}", "");
    let mut char_newline = '\n';
    let mut char_tab = '\t';
    let mut char_backslash = '\\';
    let mut char_quote = '\'';
    println!("{}", "Escaped Character Literals:");
    println!("{}", format!("{}{}" , "char_newline = ", char_newline));
    println!("{}", format!("{}{}" , "char_tab = ", char_tab));
    println!("{}", format!("{}{}" , "char_backslash = ", char_backslash));
    println!("{}", format!("{}{}" , "char_quote = ", char_quote));
    println!("{}", "");
    println!("{}", "Character in Expressions:");
    println!("{}", format!("{}{}" , format!("{}{}" , "Letter: ", char_a), " is a character"));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "Multiple chars: ", char_a), char_digit), char_symbol));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "With spaces: ", char_a), char_space), char_digit));
    println!("{}", "");
    println!("{}", "Character Comparisons:");
    println!("{}", format!("{}{}" , "char_a == 'A': ", (char_a == 'A')));
    println!("{}", format!("{}{}" , "char_digit == '5': ", (char_digit == '5')));
    println!("{}", format!("{}{}" , "char_a != char_digit: ", (char_a != char_digit)));
    println!("{}", "=========================================");
    println!("{}", "Character Operations Test Complete");
    println!("{}", "=========================================");
}

# Arrays Test
# Testing array functionality if supported

say "========================================="
say "Arrays Test"
say "========================================="

# Basic array declarations
set numbers = [1, 2, 3, 4, 5]
set strings = ["hello", "world", "test"]
set booleans = [true, false, true]
set mixed = [1, "hello", true, 3.14]

say "Basic Arrays:"
say "numbers = " + numbers
say "strings = " + strings
say "booleans = " + booleans
say "mixed = " + mixed
say ""

# Empty arrays
set empty_array = []
say "Empty Array:"
say "empty_array = " + empty_array
say ""

# Array access (if supported)
say "Array Access:"
say "numbers[0] = " + numbers[0]
say "numbers[1] = " + numbers[1]
say "strings[0] = " + strings[0]
say "strings[1] = " + strings[1]
say ""

# Array length (if supported)
say "Array Length:"
say "length of numbers = " + len(numbers)
say "length of strings = " + len(strings)
say "length of empty_array = " + len(empty_array)
say ""

# Array operations
say "Array Operations:"
set first_num = numbers[0]
set second_num = numbers[1]
set sum = first_num + second_num
say "First two numbers sum: " + first_num + " + " + second_num + " = " + sum
say ""

# Nested arrays (if supported)
set nested = [[1, 2], [3, 4], [5, 6]]
say "Nested Arrays:"
say "nested = " + nested
say "nested[0] = " + nested[0]
say "nested[0][0] = " + nested[0][0]

say "========================================="
say "Arrays Test Complete"
say "========================================="

use std::io::{self, Write};


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Simple Character Test");
    println!("{}", "=========================================");
    let mut char_a = 'A';
    let mut char_digit = '5';
    println!("{}", "Basic Character Literals:");
    println!("{}", format!("{}{}" , "char_a = ", char_a));
    println!("{}", format!("{}{}" , "char_digit = ", char_digit));
    println!("{}", "=========================================");
    println!("{}", "Simple Character Test Complete");
    println!("{}", "=========================================");
}

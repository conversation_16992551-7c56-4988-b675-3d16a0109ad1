# 📑 Dolet Compiler Changelog

## [v1.0.0-beta] - 2025-06-17

### Language Features
- Full core data types (int, float, string, bool, arrays)
- Variable declarations, constants, assignments
- Arithmetic and logical operations
- Control flow: if/else, while, for, for-in loops
- User-defined and built-in functions
- Array creation, indexing, and manipulation
- Complex string operations with full type inference
- File I/O support (read, write)
- User interaction (input/output)
- Nested expression parsing and evaluation

### Compiler Architecture
- Hyperd Tokenizer active
- Enhanced Type Inference (Token-based)
- Full error handling system integrated
- Performance optimizations active (sub-1ms compile time)
- Parallel code generation support

🎯 Core language is now stable and ready for 1.0!

{"rustc": 16591470773350601817, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 3592778941406178886, "path": 3674464971012850586, "deps": [[555019317135488525, "regex_automata", false, 13159970070688796553], [2779309023524819297, "aho_corasick", false, 3010760254504486888], [9408802513701742484, "regex_syntax", false, 11588723502871845258], [15932120279885307830, "memchr", false, 18097854924189421549]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-56a1c055f13910d7\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
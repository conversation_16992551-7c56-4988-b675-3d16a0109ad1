# Simple String Escape Test
# Testing basic string escaping without character literals

say "========================================="
say "Simple String Escape Test"
say "========================================="

# Basic string with quotes
set quoted_string = "She said Hello World"
say "Basic string: " + quoted_string

# String with escaped quotes (this should work now)
set escaped_quote = "She said \"Hello World\""
say "Escaped quotes: " + escaped_quote

say "========================================="
say "Simple String Escape Test Complete"
say "========================================="

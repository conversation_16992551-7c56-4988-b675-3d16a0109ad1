# Basic Performance Test
# Testing computational performance with simple operations

say "========================================="
say "Basic Performance Test"
say "========================================="

# Large number calculations
say "Large Number Calculations:"
set large1 = 999999
set large2 = 888888
set large3 = 777777

set large_sum = large1 + large2 + large3
set large_product = large1 * 2
set large_division = large1 / 3

say "Sum: " + large1 + " + " + large2 + " + " + large3 + " = " + large_sum
say "Product: " + large1 + " * 2 = " + large_product
say "Division: " + large1 + " / 3 = " + large_division
say ""

# Simple loop performance test
say "Simple Loop Performance Test:"
set counter = 0
set sum = 0

while counter < 100:
    set sum = sum + counter
    set counter = counter + 1
end

say "Sum of numbers 0 to 99: " + sum
say ""

# Array processing performance
say "Array Processing Performance:"
set numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
set array_sum = 0
set i = 0

while i < 10:
    set array_sum = array_sum + numbers[i]
    set i = i + 1
end

say "Array sum: " + array_sum
say ""

# Simple function performance
fun simple_add(a: int, b: int) -> int:
    return a + b
end

fun simple_multiply(a: int, b: int) -> int:
    return a * b
end

say "Function Call Performance:"
set func_result1 = simple_add(100, 200)
set func_result2 = simple_multiply(25, 4)
set func_result3 = simple_add(func_result1, func_result2)

say "simple_add(100, 200) = " + func_result1
say "simple_multiply(25, 4) = " + func_result2
say "Combined result = " + func_result3
say ""

# Mathematical operations
say "Mathematical Operations:"
set math_base = 100
set math_result1 = math_base * 2
set math_result2 = math_base / 4
set math_result3 = math_base % 7
set math_final = math_result1 + math_result2 - math_result3

say "Base: " + math_base
say "Doubled: " + math_result1
say "Quartered: " + math_result2
say "Modulo 7: " + math_result3
say "Final result: " + math_final
say ""

# Boolean operations
say "Boolean Operations:"
set bool1 = true
set bool2 = false
set bool3 = 10 > 5
set bool4 = 3 == 3
set bool_and = bool1 and bool3
set bool_or = bool2 or bool4
set bool_not = not bool2

say "bool1 AND bool3: " + bool1 + " AND " + bool3 + " = " + bool_and
say "bool2 OR bool4: " + bool2 + " OR " + bool4 + " = " + bool_or
say "NOT bool2: NOT " + bool2 + " = " + bool_not
say ""

# Comparison operations
say "Comparison Operations:"
set comp1 = 100
set comp2 = 50
set comp_gt = comp1 > comp2
set comp_lt = comp1 < comp2
set comp_eq = comp1 == comp2
set comp_ne = comp1 != comp2

say comp1 + " > " + comp2 + " = " + comp_gt
say comp1 + " < " + comp2 + " = " + comp_lt
say comp1 + " == " + comp2 + " = " + comp_eq
say comp1 + " != " + comp2 + " = " + comp_ne
say ""

# Array operations
say "Array Operations:"
set perf_array = [10, 20, 30, 40, 50]
set doubled0 = perf_array[0] * 2
set doubled1 = perf_array[1] * 2
set doubled2 = perf_array[2] * 2
set total_doubled = doubled0 + doubled1 + doubled2

say "Array elements doubled and summed:"
say perf_array[0] + " * 2 = " + doubled0
say perf_array[1] + " * 2 = " + doubled1
say perf_array[2] + " * 2 = " + doubled2
say "Total: " + total_doubled
say ""

# String operations performance
say "String Operations Performance:"
set str1 = "Performance"
set str2 = "Test"
set str3 = "Complete"
set combined = str1 + " " + str2 + " " + str3

say "String concatenation: '" + combined + "'"
say ""

# Nested arithmetic
say "Nested Arithmetic:"
set nested1 = (10 + 5) * 3
set nested2 = (20 - 8) / 4
set nested3 = (7 * 6) % 10
set nested_final = nested1 + nested2 + nested3

say "(10 + 5) * 3 = " + nested1
say "(20 - 8) / 4 = " + nested2
say "(7 * 6) % 10 = " + nested3
say "Sum: " + nested_final

say "========================================="
say "Basic Performance Test Complete"
say "========================================="

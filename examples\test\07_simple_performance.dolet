# Simple Performance Test
# Testing computational performance with working features

say "========================================="
say "Simple Performance Test"
say "========================================="

# Large number calculations
say "Large Number Calculations:"
set large1 = 999999
set large2 = 888888
set large3 = 777777

set large_sum = large1 + large2 + large3
set large_product = large1 * 2
set large_division = large1 / 3

say "Sum: " + large1 + " + " + large2 + " + " + large3 + " = " + large_sum
say "Product: " + large1 + " * 2 = " + large_product
say "Division: " + large1 + " / 3 = " + large_division
say ""

# Loop performance test
say "Loop Performance Test:"
set counter = 0
set sum = 0

while counter < 100:
    set sum = sum + counter
    set counter = counter + 1
end

say "Sum of numbers 0 to 99: " + sum
say ""

# Array processing performance
say "Array Processing Performance:"
set numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
set array_sum = 0
set i = 0

while i < 10:
    set array_sum = array_sum + numbers[i]
    set i = i + 1
end

say "Array sum: " + array_sum
say ""

# Mathematical operations performance
say "Mathematical Operations Performance:"
set math_result = 0
set operations = 0

while operations < 50:
    set temp_calc = operations * 2
    set temp_div = operations / 2
    set temp_mod = operations % 3
    set math_result = math_result + temp_calc + temp_div - temp_mod
    set operations = operations + 1
end

say "Complex math operations result: " + math_result
say ""

# Simple function performance
fun simple_add(a: int, b: int) -> int:
    return a + b
end

fun simple_multiply(a: int, b: int) -> int:
    return a * b
end

say "Function Call Performance:"
set func_result1 = simple_add(100, 200)
set func_result2 = simple_multiply(25, 4)
set func_result3 = simple_add(func_result1, func_result2)

say "simple_add(100, 200) = " + func_result1
say "simple_multiply(25, 4) = " + func_result2
say "Combined result = " + func_result3
say ""

# Boolean operations performance
say "Boolean Operations Performance:"
set bool_operations = 0
set bool_result = true

while bool_operations < 100:
    set is_even = bool_operations % 2 == 0
    set is_divisible_by_3 = bool_operations % 3 == 0
    set bool_result = bool_result and is_even
    set bool_result = bool_result or is_divisible_by_3
    set bool_operations = bool_operations + 1
end

say "Boolean operations completed, result: " + bool_result
say ""

# Array operations performance
say "Array Operations Performance:"
set perf_array = [10, 20, 30, 40, 50]
set array_operations = 0
set array_result = 0

while array_operations < 5:
    set doubled = perf_array[array_operations] * 2
    set array_result = array_result + doubled
    set array_operations = array_operations + 1
end

say "Array operations result: " + array_result
say ""

# Comparison operations performance
say "Comparison Operations Performance:"
set comparisons = 0
set comparison_results = 0

while comparisons < 100:
    if comparisons > 50:
        set comparison_results = comparison_results + 1
    end
    if comparisons % 10 == 0:
        set comparison_results = comparison_results + 2
    end
    set comparisons = comparisons + 1
end

say "Comparison operations result: " + comparison_results
say ""

# Nested calculations
say "Nested Calculations:"
set nested_result = 0
set outer = 0

while outer < 10:
    set inner = 0
    while inner < 10:
        set nested_result = nested_result + (outer * inner)
        set inner = inner + 1
    end
    set outer = outer + 1
end

say "Nested calculation result: " + nested_result

say "========================================="
say "Simple Performance Test Complete"
say "========================================="

# Simple Arrays Test
# Testing basic array functionality that works

say "========================================="
say "Simple Arrays Test"
say "========================================="

# Basic array declarations
set numbers = [1, 2, 3, 4, 5]
set strings = ["hello", "world", "test"]
set booleans = [true, false, true]

say "Basic Arrays (individual access):"
say "numbers[0] = " + numbers[0]
say "numbers[1] = " + numbers[1]
say "numbers[2] = " + numbers[2]
say "numbers[3] = " + numbers[3]
say "numbers[4] = " + numbers[4]
say ""

say "String Array Access:"
say "strings[0] = " + strings[0]
say "strings[1] = " + strings[1]
say "strings[2] = " + strings[2]
say ""

say "Boolean Array Access:"
say "booleans[0] = " + booleans[0]
say "booleans[1] = " + booleans[1]
say "booleans[2] = " + booleans[2]
say ""

# Array operations
say "Array Operations:"
set first_num = numbers[0]
set second_num = numbers[1]
set third_num = numbers[2]
set sum = first_num + second_num + third_num
say "First three numbers: " + first_num + ", " + second_num + ", " + third_num
say "Sum: " + first_num + " + " + second_num + " + " + third_num + " = " + sum
say ""

# Array with calculations
set calculated = [1 + 1, 2 * 3, 10 / 2, 15 % 4]
say "Calculated Array:"
say "calculated[0] = 1 + 1 = " + calculated[0]
say "calculated[1] = 2 * 3 = " + calculated[1]
say "calculated[2] = 10 / 2 = " + calculated[2]
say "calculated[3] = 15 % 4 = " + calculated[3]
say ""

# Array with variables
set a = 10
set b = 20
set c = 30
set var_array = [a, b, c]
say "Array with Variables:"
say "var_array[0] = " + var_array[0]
say "var_array[1] = " + var_array[1]
say "var_array[2] = " + var_array[2]
say ""

# Array comparisons
say "Array Comparisons:"
say "numbers[0] < numbers[1]: " + (numbers[0] < numbers[1])
say "numbers[4] > numbers[0]: " + (numbers[4] > numbers[0])
say "strings[0] == strings[0]: " + (strings[0] == strings[0])
say "booleans[0] != booleans[1]: " + (booleans[0] != booleans[1])
say ""

# Using array elements in functions (if functions work with arrays)
fun sum_two(x: int, y: int) -> int:
    return x + y
end

fun concat_strings(s1: string, s2: string) -> string:
    return s1 + " " + s2
end

say "Array Elements in Functions:"
say "sum_two(numbers[0], numbers[1]) = " + sum_two(numbers[0], numbers[1])
say "concat_strings(strings[0], strings[1]) = " + concat_strings(strings[0], strings[1])
say ""

# Empty array access (testing bounds)
set empty_nums = []
say "Empty Array Test:"
say "Empty array created"
# Note: Don't access empty_nums[0] as it would cause an error

# Different array sizes
set single = [42]
set pair = [1, 2]
set triple = [10, 20, 30]

say "Different Array Sizes:"
say "single[0] = " + single[0]
say "pair[0] = " + pair[0] + ", pair[1] = " + pair[1]
say "triple[0] = " + triple[0] + ", triple[1] = " + triple[1] + ", triple[2] = " + triple[2]

say "========================================="
say "Simple Arrays Test Complete"
say "========================================="

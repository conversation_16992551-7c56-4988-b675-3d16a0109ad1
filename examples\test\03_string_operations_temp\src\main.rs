use std::io::{self, Write};


fn main() {
    println!("{}", "=========================================");
    println!("{}", "String Operations Test");
    println!("{}", "=========================================");
    let mut str1 = "Hello";
    let mut str2 = "World";
    let mut str3 = "";
    let mut str4 = " ";
    let mut str5 = "123";
    let mut str6 = "Test";
    println!("{}", "Basic String Variables:");
    println!("{}", format!("{}{}" , format!("{}{}" , "str1 = '", str1), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , "str2 = '", str2), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , "str3 = '", str3), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , "str4 = '", str4), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , "str5 = '", str5), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , "str6 = '", str6), "'"));
    println!("{}", "");
    let mut concat1 = (str1 + str2);
    let mut concat2 = format!("{}{}" , format!("{}{}" , str1, " "), str2);
    let mut concat3 = ((str1 + str3) + str2);
    let mut concat4 = ((str3 + str1) + str3);
    let mut concat5 = ((str1 + str4) + str2);
    println!("{}", "String Concatenation:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "'", str1), "' + '"), str2), "' = '"), concat1), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "'", str1), "' + ' ' + '"), str2), "' = '"), concat2), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "'", str1), "' + '"), str3), "' + '"), str2), "' = '"), concat3), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "'", str3), "' + '"), str1), "' + '"), str3), "' = '"), concat4), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "'", str1), "' + '"), str4), "' + '"), str2), "' = '"), concat5), "'"));
    println!("{}", "");
    let mut num1 = 42;
    let mut num2 = 3.14f32;
    let mut bool1 = true;
    let mut bool2 = false;
    let mut str_num1 = format!("{}{}" , "Number: ", num1);
    let mut str_num2 = format!("{}{}" , "Pi is approximately ", num2);
    let mut str_bool1 = format!("{}{}" , "Is active: ", bool1);
    let mut str_bool2 = format!("{}{}" , "Is complete: ", bool2);
    println!("{}", "String with Numbers and Booleans:");
    println!("{}", str_num1);
    println!("{}", str_num2);
    println!("{}", str_bool1);
    println!("{}", str_bool2);
    println!("{}", "");
    let mut complex1 = format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Result: ", num1), " + "), num2), " = "), (num1 + num2));
    let mut complex2 = format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , str1, " "), str2), "! Today is "), bool1), " day.");
    let mut complex3 = format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Values: [", num1), ", "), num2), ", "), bool1), "]");
    println!("{}", "Complex String Operations:");
    println!("{}", complex1);
    println!("{}", complex2);
    println!("{}", complex3);
    println!("{}", "");
    let mut special1 = "Quote: \\"Hello World\\"";
    let mut special2 = "Path: C:\\Users\\<USER>\nSecond line";
    let mut special5 = "Tab test\tTabbed text";
    println!("{}", "Special Characters:");
    println!("{}", special1);
    println!("{}", special2);
    println!("{}", special3);
    println!("{}", special4);
    println!("{}", special5);
    println!("{}", "");
    let mut empty = "";
    let mut spaces = "   ";
    let mut tabs = "\t\t";
    let mut mixed_whitespace = " \t \t ";
    let mut empty_concat = format!("{}{}" , empty, "Added");
    let mut spaces_concat = format!("{}{}" , format!("{}{}" , "Before", spaces), "After");
    let mut whitespace_test = format!("{}{}" , format!("{}{}" , "[", mixed_whitespace), "]");
    println!("{}", "Empty and Whitespace Strings:");
    println!("{}", format!("{}{}" , format!("{}{}" , "Empty + 'Added' = '", empty_concat), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , "'Before' + spaces + 'After' = '", spaces_concat), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , "Mixed whitespace = '", whitespace_test), "'"));
    println!("{}", "");
    let mut long_str1 = "This is a very long string that contains many words and should test the string handling capabilities of the language.";
    let mut long_str2 = "Another long string for concatenation testing purposes with different content and structure.";
    let mut long_concat = format!("{}{}" , format!("{}{}" , long_str1, " "), long_str2);
    println!("{}", "Long Strings:");
    println!("{}", format!("{}{}" , "Long string 1: ", long_str1));
    println!("{}", format!("{}{}" , "Long string 2: ", long_str2));
    println!("{}", format!("{}{}" , "Concatenated: ", long_concat));
    println!("{}", "");
    let mut repeat_base = "Test";
    let mut repeat1 = (repeat_base + repeat_base);
    let mut repeat2 = (repeat1 + repeat1);
    let mut repeat3 = (repeat2 + repeat2);
    println!("{}", "Repeated Concatenation:");
    println!("{}", format!("{}{}" , format!("{}{}" , "Base: '", repeat_base), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , "x2: '", repeat1), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , "x4: '", repeat2), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , "x8: '", repeat3), "'"));
    println!("{}", "");
    let mut mixed_string = format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Mixed: ", 42), ", "), 3.14159f32), ", "), true), ", "), "null"), ", '"), /* unsupported expr: Char('A') */), "'");
    println!("{}", "Mixed Data Types in String:");
    println!("{}", mixed_string);
    println!("{}", "=========================================");
    println!("{}", "String Operations Test Complete");
    println!("{}", "=========================================");
}

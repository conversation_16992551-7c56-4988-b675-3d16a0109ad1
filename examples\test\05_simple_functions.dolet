# Simple Functions Test
# Testing basic function declarations and calls

say "========================================="
say "Simple Functions Test"
say "========================================="

# Simple functions with no parameters
fun greet() -> string:
    return "Hello from function!"
end

fun get_pi() -> float:
    return 3.14159
end

fun get_answer() -> int:
    return 42
end

fun is_ready() -> bool:
    return true
end

say "Simple Functions (No Parameters):"
say "greet() = " + greet()
say "get_pi() = " + get_pi()
say "get_answer() = " + get_answer()
say "is_ready() = " + is_ready()
say ""

# Functions with single parameter
fun square(n: int) -> int:
    return n * n
end

fun double_value(x: float) -> float:
    return x * 2.0
end

fun is_positive(num: int) -> bool:
    return num > 0
end

say "Single Parameter Functions:"
say "square(5) = " + square(5)
say "square(-3) = " + square(-3)
say "double_value(2.5) = " + double_value(2.5)
say "is_positive(10) = " + is_positive(10)
say "is_positive(-5) = " + is_positive(-5)
say ""

# Functions with multiple parameters
fun add(a: int, b: int) -> int:
    return a + b
end

fun multiply(x: float, y: float) -> float:
    return x * y
end

fun max_of_two(a: int, b: int) -> int:
    if a > b:
        return a
    else:
        return b
    end
end

say "Multiple Parameter Functions:"
say "add(10, 5) = " + add(10, 5)
say "add(-3, 8) = " + add(-3, 8)
say "multiply(2.5, 4.0) = " + multiply(2.5, 4.0)
say "max_of_two(15, 7) = " + max_of_two(15, 7)
say "max_of_two(3, 12) = " + max_of_two(3, 12)
say ""

# Functions calling other functions
fun circle_area(radius: float) -> float:
    return get_pi() * square_float(radius)
end

fun square_float(n: float) -> float:
    return n * n
end

say "Functions Calling Functions:"
say "circle_area(3.0) = " + circle_area(3.0)
say ""

# Simple recursive functions
fun factorial(n: int) -> int:
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)
    end
end

say "Recursive Functions:"
say "factorial(5) = " + factorial(5)
say "factorial(0) = " + factorial(0)
say "factorial(3) = " + factorial(3)
say ""

# Functions with simple logic
fun grade_calculator(score: int) -> string:
    if score >= 90:
        return "A"
    else:
        if score >= 80:
            return "B"
        else:
            if score >= 70:
                return "C"
            else:
                return "F"
            end
        end
    end
end

fun is_even(n: int) -> bool:
    return n % 2 == 0
end

say "Logic Functions:"
say "grade_calculator(95) = " + grade_calculator(95)
say "grade_calculator(75) = " + grade_calculator(75)
say "grade_calculator(55) = " + grade_calculator(55)
say "is_even(10) = " + is_even(10)
say "is_even(7) = " + is_even(7)
say ""

# Testing function calls in expressions
set result1 = add(5, 3)
set result2 = multiply(2.0, 3.5)
set result3 = is_positive(result1)

say "Function Results in Variables:"
say "result1 = add(5, 3) = " + result1
say "result2 = multiply(2.0, 3.5) = " + result2
say "result3 = is_positive(result1) = " + result3

say "========================================="
say "Simple Functions Test Complete"
say "========================================="

use std::io::{self, Write};

fn simple_add(a: i64, b: i64) -> i64 {
    return (a + b);
}

fn simple_multiply(a: i64, b: i64) -> i64 {
    return (a * b);
}


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Basic Performance Test");
    println!("{}", "=========================================");
    println!("{}", "Large Number Calculations:");
    let mut large1 = 999999;
    let mut large2 = 888888;
    let mut large3 = 777777;
    let mut large_sum = ((large1 + large2) + large3);
    let mut large_product = (large1 * 2);
    let mut large_division = (large1 / 3);
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Sum: ", large1), " + "), large2), " + "), large3), " = "), large_sum));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "Product: ", large1), " * 2 = "), large_product));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "Division: ", large1), " / 3 = "), large_division));
    println!("{}", "");
    println!("{}", "Simple Loop Performance Test:");
    let mut counter = 0;
    let mut sum = 0;
    while (counter < 100) {
        sum = (sum + counter);
        counter = (counter + 1);
    }
    println!("{}", format!("{}{}" , "Sum of numbers 0 to 99: ", sum));
    println!("{}", "");
    println!("{}", "Array Processing Performance:");
    let mut numbers = vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    let mut array_sum = 0;
    let mut i = 0;
    while (i < 10) {
        array_sum = (array_sum + numbers[i as usize]);
        i = (i + 1);
    }
    println!("{}", format!("{}{}" , "Array sum: ", array_sum));
    println!("{}", "");
    println!("{}", "Function Call Performance:");
    let mut func_result1 = simple_add(100, 200);
    let mut func_result2 = simple_multiply(25, 4);
    let mut func_result3 = simple_add(func_result1, func_result2);
    println!("{}", format!("{}{}" , "simple_add(100, 200) = ", func_result1));
    println!("{}", format!("{}{}" , "simple_multiply(25, 4) = ", func_result2));
    println!("{}", format!("{}{}" , "Combined result = ", func_result3));
    println!("{}", "");
    println!("{}", "Mathematical Operations:");
    let mut math_base = 100;
    let mut math_result1 = (math_base * 2);
    let mut math_result2 = (math_base / 4);
    let mut math_result3 = (math_base % 7);
    let mut math_final = ((math_result1 + math_result2) - math_result3);
    println!("{}", format!("{}{}" , "Base: ", math_base));
    println!("{}", format!("{}{}" , "Doubled: ", math_result1));
    println!("{}", format!("{}{}" , "Quartered: ", math_result2));
    println!("{}", format!("{}{}" , "Modulo 7: ", math_result3));
    println!("{}", format!("{}{}" , "Final result: ", math_final));
    println!("{}", "");
    println!("{}", "Boolean Operations:");
    let mut bool1 = true;
    let mut bool2 = false;
    let mut bool3 = (10 > 5);
    let mut bool4 = (3 == 3);
    let mut bool_and = (bool1 && bool3);
    let mut bool_or = (bool2 || bool4);
    let mut bool_not = (!bool2);
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "bool1 AND bool3: ", bool1), " AND "), bool3), " = "), bool_and));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "bool2 OR bool4: ", bool2), " OR "), bool4), " = "), bool_or));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "NOT bool2: NOT ", bool2), " = "), bool_not));
    println!("{}", "");
    println!("{}", "Comparison Operations:");
    let mut comp1 = 100;
    let mut comp2 = 50;
    let mut comp_gt = (comp1 > comp2);
    let mut comp_lt = (comp1 < comp2);
    let mut comp_eq = (comp1 == comp2);
    let mut comp_ne = (comp1 != comp2);
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , comp1, " > "), comp2), " = "), comp_gt));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , comp1, " < "), comp2), " = "), comp_lt));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , comp1, " == "), comp2), " = "), comp_eq));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , comp1, " != "), comp2), " = "), comp_ne));
    println!("{}", "");
    println!("{}", "Array Operations:");
    let mut perf_array = vec![10, 20, 30, 40, 50];
    let mut doubled0 = (perf_array[0 as usize] * 2);
    let mut doubled1 = (perf_array[1 as usize] * 2);
    let mut doubled2 = (perf_array[2 as usize] * 2);
    let mut total_doubled = ((doubled0 + doubled1) + doubled2);
    println!("{}", "Array elements doubled and summed:");
    println!("{}", format!("{}{}" , format!("{}{}" , perf_array[0 as usize], " * 2 = "), doubled0));
    println!("{}", format!("{}{}" , format!("{}{}" , perf_array[1 as usize], " * 2 = "), doubled1));
    println!("{}", format!("{}{}" , format!("{}{}" , perf_array[2 as usize], " * 2 = "), doubled2));
    println!("{}", format!("{}{}" , "Total: ", total_doubled));
    println!("{}", "");
    println!("{}", "String Operations Performance:");
    let mut str1 = "Performance";
    let mut str2 = "Test";
    let mut str3 = "Complete";
    let mut combined = format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , str1, " "), str2), " "), str3);
    println!("{}", format!("{}{}" , format!("{}{}" , "String concatenation: '", combined), "'"));
    println!("{}", "");
    println!("{}", "Nested Arithmetic:");
    let mut nested1 = ((10 + 5) * 3);
    let mut nested2 = ((20 - 8) / 4);
    let mut nested3 = ((7 * 6) % 10);
    let mut nested_final = ((nested1 + nested2) + nested3);
    println!("{}", format!("{}{}" , "(10 + 5) * 3 = ", nested1));
    println!("{}", format!("{}{}" , "(20 - 8) / 4 = ", nested2));
    println!("{}", format!("{}{}" , "(7 * 6) % 10 = ", nested3));
    println!("{}", format!("{}{}" , "Sum: ", nested_final));
    println!("{}", "=========================================");
    println!("{}", "Basic Performance Test Complete");
    println!("{}", "=========================================");
}

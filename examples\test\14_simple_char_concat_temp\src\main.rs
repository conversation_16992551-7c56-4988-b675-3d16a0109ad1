use std::io::{self, Write};


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Simple Character Concatenation Test");
    println!("{}", "=========================================");
    let mut char_a = 'A';
    let mut char_b = 'B';
    println!("{}", "Basic Character Concatenation:");
    println!("{}", format!("{}{}" , "char_a = ", char_a));
    println!("{}", format!("{}{}" , "char_b = ", char_b));
    println!("{}", format!("{}{}" , format!("{}{}" , "Combined: ", char_a), char_b));
    println!("{}", "=========================================");
    println!("{}", "Simple Character Concatenation Test Complete");
    println!("{}", "=========================================");
}

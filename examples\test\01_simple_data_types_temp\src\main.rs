use std::io::{self, Write};


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Simple Data Types Test");
    println!("{}", "=========================================");
    let mut small_int = 42;
    let mut large_int = 999999;
    let mut negative_int = (-123);
    let mut zero_int = 0;
    println!("{}", "Integer Types:");
    println!("{}", format!("{}{}" , "small_int = ", small_int));
    println!("{}", format!("{}{}" , "large_int = ", large_int));
    println!("{}", format!("{}{}" , "negative_int = ", negative_int));
    println!("{}", format!("{}{}" , "zero_int = ", zero_int));
    println!("{}", "");
    let mut simple_float = 3.14f32;
    let mut precise_float = 2.71828f32;
    let mut negative_float = (-3.14159f32);
    let mut zero_float = 0f32;
    println!("{}", "Float Types:");
    println!("{}", format!("{}{}" , "simple_float = ", simple_float));
    println!("{}", format!("{}{}" , "precise_float = ", precise_float));
    println!("{}", format!("{}{}" , "negative_float = ", negative_float));
    println!("{}", format!("{}{}" , "zero_float = ", zero_float));
    println!("{}", "");
    let mut pi_double = 3.141592653589793f64;
    let mut e_double = 2.718281828459045f64;
    let mut negative_double = (-2.718281828459045f64);
    println!("{}", "Double Types:");
    println!("{}", format!("{}{}" , "pi_double = ", pi_double));
    println!("{}", format!("{}{}" , "e_double = ", e_double));
    println!("{}", format!("{}{}" , "negative_double = ", negative_double));
    println!("{}", "");
    let mut true_bool = true;
    let mut false_bool = false;
    let mut bool_from_comparison = (5 > 3);
    let mut bool_from_equality = (10 == 10);
    println!("{}", "Boolean Types:");
    println!("{}", format!("{}{}" , "true_bool = ", true_bool));
    println!("{}", format!("{}{}" , "false_bool = ", false_bool));
    println!("{}", format!("{}{}" , "bool_from_comparison = ", bool_from_comparison));
    println!("{}", format!("{}{}" , "bool_from_equality = ", bool_from_equality));
    println!("{}", "");
    let mut simple_string = "Hello World";
    let mut empty_string = "";
    let mut string_with_numbers = "Test123";
    let mut string_with_spaces = "   Spaces   ";
    println!("{}", "String Types:");
    println!("{}", format!("{}{}" , "simple_string = ", simple_string));
    println!("{}", format!("{}{}" , format!("{}{}" , "empty_string = '", empty_string), "'"));
    println!("{}", format!("{}{}" , "string_with_numbers = ", string_with_numbers));
    println!("{}", format!("{}{}" , format!("{}{}" , "string_with_spaces = '", string_with_spaces), "'"));
    println!("{}", "");
    let mut null_value = "null";
    let mut nullable_int = "null";
    println!("{}", "Null Types:");
    println!("{}", format!("{}{}" , "null_value = ", null_value));
    println!("{}", format!("{}{}" , "nullable_int = ", nullable_int));
    println!("{}", "");
    let mut mixed_concat = format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Number: ", 42), ", Float: "), 3.14f32), ", Bool: "), true);
    let mut complex_expression = format!("{}{}" , format!("{}{}" , format!("{}{}" , "Result: ", (10 + 5)), " is "), (10 > 5));
    println!("{}", "Type Mixing:");
    println!("{}", format!("{}{}" , "mixed_concat = ", mixed_concat));
    println!("{}", format!("{}{}" , "complex_expression = ", complex_expression));
    println!("{}", "=========================================");
    println!("{}", "Simple Data Types Test Complete");
    println!("{}", "=========================================");
}

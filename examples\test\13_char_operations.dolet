# Character Operations Test
# Testing character literals and operations

say "========================================="
say "Character Operations Test"
say "========================================="

# Basic character literals
set char_a = 'A'
set char_digit = '5'
set char_symbol = '@'
set char_space = ' '

say "Basic Character Literals:"
say "char_a = " + char_a
say "char_digit = " + char_digit
say "char_symbol = " + char_symbol
say "char_space = '" + char_space + "'"
say ""

# Escaped character literals
set char_newline = '\n'
set char_tab = '\t'
set char_backslash = '\\'
set char_quote = '\''

say "Escaped Character Literals:"
say "char_newline = " + char_newline
say "char_tab = " + char_tab
say "char_backslash = " + char_backslash
say "char_quote = " + char_quote
say ""

# Character in expressions
say "Character in Expressions:"
say "Letter: " + char_a + " is a character"
say "Multiple chars: " + char_a + char_digit + char_symbol
say "With spaces: " + char_a + char_space + char_digit
say ""

# Character comparisons
say "Character Comparisons:"
say "char_a == 'A': " + (char_a == 'A')
say "char_digit == '5': " + (char_digit == '5')
say "char_a != char_digit: " + (char_a != char_digit)

say "========================================="
say "Character Operations Test Complete"
say "========================================="

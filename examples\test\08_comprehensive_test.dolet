# Comprehensive Language Test
# Testing multiple language features together

say "========================================="
say "Comprehensive Language Test"
say "========================================="

# Data types and variables
say "=== Data Types and Variables ==="
set int_var = 42
set float_var = 3.14159
set string_var = "Hello World"
set bool_var = true
set null_var = null

say "Integer: " + int_var
say "Float: " + float_var
say "String: " + string_var
say "Boolean: " + bool_var
say "Null: " + null_var
say ""

# Arrays
say "=== Arrays ==="
set numbers = [1, 2, 3, 4, 5]
set words = ["apple", "banana", "cherry"]
set flags = [true, false, true, false]

say "Numbers array: " + numbers[0] + ", " + numbers[1] + ", " + numbers[2]
say "Words array: " + words[0] + ", " + words[1] + ", " + words[2]
say "Flags array: " + flags[0] + ", " + flags[1] + ", " + flags[2]
say ""

# Arithmetic operations
say "=== Arithmetic Operations ==="
set a = 15
set b = 4
say "a = " + a + ", b = " + b
say "Addition: " + a + " + " + b + " = " + (a + b)
say "Subtraction: " + a + " - " + b + " = " + (a - b)
say "Multiplication: " + a + " * " + b + " = " + (a * b)
say "Division: " + a + " / " + b + " = " + (a / b)
say "Modulo: " + a + " % " + b + " = " + (a % b)
say ""

# Boolean operations
say "=== Boolean Operations ==="
set x = true
set y = false
say "x = " + x + ", y = " + y
say "x AND y: " + (x and y)
say "x OR y: " + (x or y)
say "NOT x: " + (not x)
say "NOT y: " + (not y)
say ""

# Comparison operations
say "=== Comparison Operations ==="
set p = 10
set q = 20
say "p = " + p + ", q = " + q
say "p == q: " + (p == q)
say "p != q: " + (p != q)
say "p < q: " + (p < q)
say "p > q: " + (p > q)
say "p <= q: " + (p <= q)
say "p >= q: " + (p >= q)
say ""

# Control flow - if/else
say "=== Control Flow - If/Else ==="
set score = 85
say "Score: " + score

if score >= 90:
    say "Grade: A (Excellent)"
else:
    if score >= 80:
        say "Grade: B (Good)"
    else:
        if score >= 70:
            say "Grade: C (Average)"
        else:
            say "Grade: D (Below Average)"
        end
    end
end
say ""

# Control flow - loops
say "=== Control Flow - Loops ==="
say "While loop (counting to 5):"
set counter = 1
while counter <= 5:
    say "Count: " + counter
    set counter = counter + 1
end

say "For loop (1 to 5):"
for i from 1 to 5:
    say "For loop iteration: " + i
end
say ""

# Functions
say "=== Functions ==="

fun calculate_area(length: int, width: int) -> int:
    return length * width
end

fun greet_person(name: string) -> string:
    return "Hello, " + name + "!"
end

fun is_even(num: int) -> bool:
    return num % 2 == 0
end

fun factorial(n: int) -> int:
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)
    end
end

set area = calculate_area(5, 3)
set greeting = greet_person("Alice")
set even_check = is_even(8)
set fact_5 = factorial(5)

say "Area of 5x3 rectangle: " + area
say "Greeting: " + greeting
say "Is 8 even? " + even_check
say "Factorial of 5: " + fact_5
say ""

# Array processing with functions
say "=== Array Processing ==="
set test_numbers = [2, 4, 6, 8, 10]
say "Testing even numbers:"
say "is_even(" + test_numbers[0] + ") = " + is_even(test_numbers[0])
say "is_even(" + test_numbers[1] + ") = " + is_even(test_numbers[1])
say "is_even(" + test_numbers[2] + ") = " + is_even(test_numbers[2])
say ""

# Complex expressions
say "=== Complex Expressions ==="
set complex1 = (10 + 5) * 2 - 3
set complex2 = numbers[0] + numbers[4] * 2
set complex3 = calculate_area(numbers[1], numbers[2])

say "Complex arithmetic: (10 + 5) * 2 - 3 = " + complex1
say "Array arithmetic: numbers[0] + numbers[4] * 2 = " + complex2
say "Function with array: calculate_area(numbers[1], numbers[2]) = " + complex3
say ""

# String operations
say "=== String Operations ==="
set first_name = "John"
set last_name = "Doe"
set age = 30
set full_info = first_name + " " + last_name + " is " + age + " years old"
say "Full info: " + full_info

set message = greet_person(first_name + " " + last_name)
say "Message: " + message
say ""

# Mixed data type operations
say "=== Mixed Data Type Operations ==="
set mixed_result = "Result: " + (numbers[0] + numbers[1]) + " is " + is_even(numbers[0] + numbers[1])
say mixed_result

set status = "Processing " + words[0] + " with value " + numbers[0] + " - " + flags[0]
say status
say ""

# Performance demonstration
say "=== Performance Demonstration ==="
set start_num = 1
set end_num = 10
set sum_total = 0

say "Calculating sum from " + start_num + " to " + end_num + ":"
set current = start_num
while current <= end_num:
    set sum_total = sum_total + current
    set current = current + 1
end

say "Sum: " + sum_total
say "Average: " + (sum_total / end_num)

say "========================================="
say "Comprehensive Test Complete!"
say "All major language features tested successfully."
say "========================================="

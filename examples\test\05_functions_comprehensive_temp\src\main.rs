use std::io::{self, Write};

fn greet() -> String {
    return "Hello from function!".to_string();
}

fn get_pi() -> f32 {
    return 3.14159f32;
}

fn get_answer() -> i64 {
    return 42;
}

fn is_ready() -> bool {
    return true;
}

fn square(n: i64) -> i64 {
    return (n * n);
}

fn double_value(x: f32) -> f32 {
    return (x as f32 * 2f32 as f32);
}

fn is_positive(num: i64) -> bool {
    return (num > 0);
}

fn add_prefix(text: &str) -> String {
    return format!("{}{}" , "PREFIX_", text);
}

fn add(a: i64, b: i64) -> i64 {
    return (a + b);
}

fn multiply(x: f32, y: f32) -> f32 {
    return (x as f32 * y as f32);
}

fn max_of_two(a: i64, b: i64) -> i64 {
    if (a > b) {
        return a;
    } else {
        return b;
    }
    0
}

fn concat_with_separator(str1: &str, str2: &str, sep: &str) -> String {
    return ((str1 + sep) + str2).to_string();
}

fn calculate_area(width: f32, height: f32) -> f32 {
    return (width as f32 * height as f32);
}

fn format_person(name: &str, age: i64, height: f32, is_student: bool) -> String {
    return format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , name, " (age: "), age), ", height: "), height), ", student: "), is_student), ")");
}

fn calculate_discount(price: f32, discount_percent: i64, is_member: bool) -> f32 {
    let mut discount = ((price as f32 * discount_percent as f32) / 100f32);
    if is_member {
        let mut discount = (discount * 1.5f32);
    }
    return (price as f32 - discount as f32);
}

fn circle_area(radius: f32) -> f32 {
    return (get_pi() as f32 * square_float(radius) as f32);
}

fn square_float(n: f32) -> f32 {
    return (n * n);
}

fn compound_interest(principal: f32, rate: f32, years: i64) -> f32 {
    let mut amount = principal;
    // Unsupported statement in function
    return amount;
}

fn factorial(n: i64) -> i64 {
    if (n <= 1) {
        return 1;
    } else {
        return (n * factorial((n - 1)));
    }
    0
}

fn fibonacci(n: i64) -> i64 {
    if (n <= 1) {
        return n;
    } else {
        return (fibonacci((n - 1)) + fibonacci((n - 2)));
    }
    0
}

fn grade_calculator(score: i64) -> String {
    if (score >= 90) {
        return "A".to_string();
    } else {
        if (score >= 80) {
            return "B".to_string();
        } else {
            // Unsupported nested statement
        }
    }
    String::new()
}

fn is_prime(n: i64) -> bool {
    if (n <= 1) {
        return false;
    }
    if (n <= 3) {
        return true;
    }
    if (((n % 2) == 0) || ((n % 3) == 0)) {
        return false;
    }
    let mut i = 5;
    while ((i * i) <= n) {
        if (((n % i) == 0) || ((n % (i + 2)) == 0)) {
            // Unsupported nested statement in function while loop
        }
        i = (i + 6);
    }
    return true;
}


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Functions Test");
    println!("{}", "=========================================");
    println!("{}", "Simple Functions (No Parameters):");
    println!("{}", format!("{}{}" , "greet() = ", greet()));
    println!("{}", format!("{}{}" , "get_pi() = ", get_pi()));
    println!("{}", format!("{}{}" , "get_answer() = ", get_answer()));
    println!("{}", format!("{}{}" , "is_ready() = ", is_ready()));
    println!("{}", "");
    println!("{}", "Single Parameter Functions:");
    println!("{}", format!("{}{}" , "square(5) = ", square(5)));
    println!("{}", format!("{}{}" , "square(-3) = ", square((-3))));
    println!("{}", format!("{}{}" , "double_value(2.5) = ", double_value(2.5f32)));
    println!("{}", format!("{}{}" , "is_positive(10) = ", is_positive(10)));
    println!("{}", format!("{}{}" , "is_positive(-5) = ", is_positive((-5))));
    println!("{}", format!("{}{}" , "add_prefix('test') = ", add_prefix("test".to_string())));
    println!("{}", "");
    println!("{}", "Multiple Parameter Functions:");
    println!("{}", format!("{}{}" , "add(10, 5) = ", add(10, 5)));
    println!("{}", format!("{}{}" , "add(-3, 8) = ", add((-3), 8)));
    println!("{}", format!("{}{}" , "multiply(2.5, 4.0) = ", multiply(2.5f32, 4f32)));
    println!("{}", format!("{}{}" , "max_of_two(15, 7) = ", max_of_two(15, 7)));
    println!("{}", format!("{}{}" , "max_of_two(3, 12) = ", max_of_two(3, 12)));
    println!("{}", format!("{}{}" , "concat_with_separator('Hello', 'World', ' ') = ", concat_with_separator("Hello".to_string(), "World".to_string(), " ".to_string())));
    println!("{}", format!("{}{}" , "calculate_area(5.0, 3.0) = ", calculate_area(5f32, 3f32)));
    println!("{}", "");
    println!("{}", "Mixed Parameter Types:");
    println!("{}", format!("{}{}" , "format_person('Alice', 25, 5.6, true) = ", format_person("Alice".to_string(), 25, 5.6f32, true)));
    println!("{}", format!("{}{}" , "calculate_discount(100.0, 10, true) = ", calculate_discount(100f32, 10, true)));
    println!("{}", format!("{}{}" , "calculate_discount(100.0, 10, false) = ", calculate_discount(100f32, 10, false)));
    println!("{}", "");
    println!("{}", "Functions Calling Functions:");
    println!("{}", format!("{}{}" , "circle_area(3.0) = ", circle_area(3f32)));
    println!("{}", format!("{}{}" , "compound_interest(1000.0, 5.0, 3) = ", compound_interest(1000f32, 5f32, 3)));
    println!("{}", "");
    println!("{}", "Recursive Functions:");
    println!("{}", format!("{}{}" , "factorial(5) = ", factorial(5)));
    println!("{}", format!("{}{}" , "factorial(0) = ", factorial(0)));
    println!("{}", format!("{}{}" , "fibonacci(6) = ", fibonacci(6)));
    println!("{}", format!("{}{}" , "fibonacci(1) = ", fibonacci(1)));
    println!("{}", "");
    println!("{}", "Complex Logic Functions:");
    println!("{}", format!("{}{}" , "grade_calculator(95) = ", grade_calculator(95)));
    println!("{}", format!("{}{}" , "grade_calculator(75) = ", grade_calculator(75)));
    println!("{}", format!("{}{}" , "grade_calculator(55) = ", grade_calculator(55)));
    println!("{}", format!("{}{}" , "is_prime(17) = ", is_prime(17)));
    println!("{}", format!("{}{}" , "is_prime(15) = ", is_prime(15)));
    println!("{}", format!("{}{}" , "is_prime(2) = ", is_prime(2)));
    println!("{}", "=========================================");
    println!("{}", "Functions Test Complete");
    println!("{}", "=========================================");
}

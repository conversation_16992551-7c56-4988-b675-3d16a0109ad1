# 🚀 Dolet Programming Language

A blazingly fast, modern programming language with ultra-fast compilation, comprehensive features, and native executable generation.

## ✨ Key Features

### 🔥 **Complete Language Implementation**
- **All core language features** - variables, functions, control flow, arrays, I/O
- **Built-in functions** - math, string, array operations, type conversions
- **User interaction** - input/output, file operations, interactive programs
- **Modern syntax** - clean, readable, and intuitive
- **Static typing** with smart type inference

### ⚡ **Ultra-Fast Compilation**
- **Sub-2 second compilation** for most programs
- **Direct executable generation** - no VM, no interpreter
- **Zero-copy tokenization** with optimized parsing
- **Memory-efficient compilation** with arena allocation
- **Rust-based backend** for maximum performance

### 🎯 **Production-Ready Features**
- **Logical operators** - `and`, `or`, `not` for boolean logic
- **Array operations** - indexing, iteration, manipulation
- **File I/O** - read, write, append operations
- **User input** - interactive programs with prompts
- **For-in loops** - iterate over arrays and collections
- **String operations** - concatenation, manipulation, formatting

### 🛡️ **Developer Experience**
- **Clear error messages** with line and column information
- **Fast feedback loop** - compile and run in seconds
- **Comprehensive examples** - working code for all features
- **Cross-platform** - Windows, Linux, macOS support

## 🏗️ **Language Features**

### ✅ **Variables & Data Types**
```dolet
# Type inference (automatic)
set name = "Alice"
set age = 25
set height = 5.8
set active = true
set data = null

# Explicit types
set count: int = 100
set price: float = 19.99
set weight: double = 70.5

# Constants
const PI = 3.14159
const MAX_SIZE = 1000
```

### ✅ **Control Flow**
```dolet
# If statements
if age >= 18:
    say "You are an adult"
else:
    say "You are a minor"
end

# While loops
while count < 5:
    say "Count: " + count
    set count = count + 1
end

# For loops (range-based)
for i from 1 to 5:
    say "For loop: " + i
end

# For-in loops (array iteration)
for item in numbers:
    say "Array item: " + item
end
```

### ✅ **Functions**
```dolet
# Simple function
fun greet(name):
    say "Hello, " + name + "!"
end

# Function with return value
fun add(a: int, b: int) -> int:
    return a + b
end

# Function calls
greet("Alice")
set result = add(10, 20)
```

### ✅ **Arrays & Operations**
```dolet
# Array creation
set numbers = [1, 2, 3, 4, 5]
set names = ["Alice", "Bob", "Charlie"]

# Array indexing
set first = numbers[0]
set last = numbers[4]

# Array modification
set numbers[0] = 100
```

### ✅ **Built-in Functions**
```dolet
# Math functions
set sqrt_result = sqrt(16)        # 4
set power_result = power(2, 3)    # 8
set abs_result = abs(-5)          # 5

# String functions
set str_length = length("hello")  # 5
set uppercase = upper("hello")    # "HELLO"
set lowercase = lower("WORLD")    # "world"

# Array functions
set array_length = length(numbers)  # 5
set array_sum = sum(numbers)        # 15

# Type conversion
set str_to_int = int("123")       # 123
set int_to_str = string(456)      # "456"
set str_to_float = float("3.14")  # 3.14
```

### ✅ **User Input & File Operations**
```dolet
# User input
ask "What is your name? "
set user_name = input
say "Hello, " + user_name + "!"

# File operations
write "output.txt", "Hello, World!"
set file_content = read "output.txt"
append "output.txt", "New line"
```

### ✅ **Logical & Comparison Operators**
```dolet
# Logical operators
set result1 = true and false     # false
set result2 = true or false      # true
set result3 = not true           # false

# Comparison operators
set equal = (5 == 5)             # true
set not_equal = (5 != 3)         # true
set less_than = (3 < 5)          # true
set greater_equal = (5 >= 5)     # true
```

## 🚀 **Performance Benchmarks**

Dolet achieves exceptional compilation performance:

- **File Reading**: ~120µs for typical programs
- **Tokenization**: ~20µs for lexical analysis
- **Parsing**: ~50µs for AST generation
- **Semantic Analysis**: ~10µs for type checking
- **Code Generation**: ~1.7s for Rust compilation
- **Total Time**: ~1.8s for complete executable generation

**Ultra-fast development cycle** with sub-2-second compilation for most programs.

## 🛠️ **Installation & Usage**

### Prerequisites
- Rust 1.70+ with Cargo
- Windows/Linux/macOS support

### Build the Compiler
```bash
git clone https://github.com/your-username/dolet-compiler
cd dolet-compiler
cargo build --release
```

### Compile Dolet Programs
```bash
# Basic compilation (creates executable)
cargo run --release --bin dolet -- your_program.dolet

# With timing information
cargo run --release --bin dolet -- your_program.dolet --time

# Run the generated executable
./your_program.exe  # Windows
./your_program     # Linux/macOS
```

### Command Line Options
- `--time` - Show detailed compilation timing
- `--tokens` - Print the token stream (debug)
- `--ast` - Print the Abstract Syntax Tree (debug)
- `--help` - Show help message

### Quick Start Example
```bash
# Create a simple program
echo 'say "Hello, Dolet!"' > hello.dolet

# Compile and run
cargo run --release --bin dolet -- hello.dolet --time
./hello.exe
```

## 📁 **Project Structure**

```
src/
├── main.rs              # CLI interface and compilation pipeline
├── token.rs             # Token definitions and type system
├── tokenizer.rs         # High-performance tokenizer
├── ast.rs               # Abstract Syntax Tree definitions
├── parser.rs            # Recursive descent parser
├── symbol_table.rs      # Symbol table and type checking
├── interpreter.rs       # AST interpreter (for testing)
├── c_codegen.rs         # C code generation backend
├── direct_machine_code.rs # Direct machine code generation
├── memory_pool.rs       # Memory management optimizations
├── simd_tokenizer.rs    # SIMD-optimized tokenizer
└── cache_optimized_parser.rs # Cache-optimized parsing
```

## 🔧 **Technical Architecture**

### Compilation Pipeline
1. **File Reading** - Memory-mapped I/O for large files
2. **Lexical Analysis** - High-performance tokenizer with type inference
3. **Syntax Analysis** - Recursive descent parser builds AST
4. **Semantic Analysis** - Type checking and symbol resolution
5. **Code Generation** - Rust code generation with native compilation
6. **Executable Output** - Native executable ready to run

### Key Optimizations
- **Arena allocation** - Bump allocator for AST nodes
- **Zero-copy tokenization** - String slices without allocation
- **Type inference** - Types determined during parsing
- **Memory efficiency** - Minimal memory footprint
- **Fast compilation** - Optimized for development speed

## 🎯 **Type System**

### Supported Types
- `int` - 64-bit signed integers (`42`, `-100`, `1000000`)
- `float` - 32-bit floating point (`3.14`, `2.5`)
- `double` - 64-bit floating point (`3.*********`, high precision)
- `string` - UTF-8 strings (`"Hello, World!"`)
- `char` - Single characters (`'A'`, `'5'`, `'@'`)
- `bool` - Boolean values (`true`, `false`)
- `null` - Null values (nullable types)
- `array` - Collections (`[1, 2, 3]`, `["a", "b", "c"]`)

### Smart Type Inference
Dolet automatically determines types from values:

| Input | Inferred Type | Example |
|-------|---------------|---------|
| `"text"` | `string` | `"Hello, World!"` |
| `'c'` | `char` | `'A'` |
| `42` | `int` | `1000` |
| `3.14` | `float` | `3.14159` (≤5 decimals) |
| `3.*********` | `double` | `2.718281828` (≥6 decimals) |
| `true`/`false` | `bool` | `true` |
| `null` | `null` | `null` |
| `[1, 2, 3]` | `array` | `[1, 2, 3]` |

## 🚧 **Development Status**

### ✅ **Completed Features**
- [x] **Core Language** - Variables, functions, control flow
- [x] **Data Types** - All basic types with smart inference
- [x] **Arrays** - Creation, indexing, iteration, manipulation
- [x] **Control Flow** - if/else, while, for, for-in loops
- [x] **Logical Operators** - and, or, not operations
- [x] **Built-in Functions** - Math, string, array, conversion functions
- [x] **User Input/Output** - Interactive programs with prompts
- [x] **File Operations** - Read, write, append file operations
- [x] **Native Compilation** - Direct executable generation
- [x] **Performance Optimization** - Sub-2-second compilation
- [x] **Error Handling** - Clear error messages with location info

### 🔄 **In Progress**
- [ ] **Advanced OOP** - Classes, inheritance, methods
- [ ] **Module System** - Import/export functionality
- [ ] **Error Handling** - try/catch exception handling
- [ ] **Pattern Matching** - Advanced control flow
- [ ] **Generics** - Generic types and functions

### 🎯 **Future Plans**
- [ ] **Package Manager** - Dependency management
- [ ] **Standard Library** - Comprehensive built-in modules
- [ ] **IDE Integration** - Language server protocol
- [ ] **Debugging Tools** - Integrated debugger
- [ ] **Cross-compilation** - Multiple target platforms

## 📚 **Examples & Documentation**

### Working Examples
Check out the `examples/` directory for comprehensive examples:
- `examples/test/` - Feature-specific test programs
- `examples/working_comprehensive_test.dolet` - Complete language showcase
- `syntax.dolet` - Complete syntax reference

### Learning Resources
1. **Start with basics** - Variables, functions, control flow
2. **Try interactive features** - User input and file operations
3. **Explore built-ins** - Math, string, and array functions
4. **Build real programs** - Combine features for practical applications

## 🤝 **Contributing**

We welcome contributions! Areas where help is needed:

1. **Language Features** - OOP, modules, generics, pattern matching
2. **Standard Library** - Built-in modules and functions
3. **Developer Tools** - IDE integration, debugging, profiling
4. **Performance** - Compilation speed and runtime optimizations
5. **Documentation** - Tutorials, examples, and guides
6. **Testing** - Comprehensive test coverage

## 📄 **License**

MIT License - see LICENSE file for details.

## 🙏 **Acknowledgments**

- **Rust Community** - Amazing ecosystem and performance
- **Modern Language Design** - Inspired by the best features
- **Open Source** - Built on the shoulders of giants

---

**Dolet - Fast, Modern, Powerful** 🚀

*Built for developers who value both performance and productivity*

# Test File Operations - Check if they work
say "Testing File Operations..."

# Test 1: Write to file
say "Test 1: Writing to file"
write "test_output.txt", "Hello, World!"
say "Written 'Hello, World!' to test_output.txt"

write "test_output.txt", "This is line 2"
say "Written second line to file"

# Test 2: Read from file
say "Test 2: Reading from file"
set file_content = read "test_output.txt"
say "File content: " + file_content

# Test 3: Write multiple lines
say "Test 3: Writing multiple lines"
write "multi_line.txt", "Line 1\nLine 2\nLine 3"
say "Written multiple lines to multi_line.txt"

set multi_content = read "multi_line.txt"
say "Multi-line content: " + multi_content

# Test 4: Append to file (if supported)
say "Test 4: Append to file"
append "test_output.txt", "\nAppended line"
say "Appended line to file"

set final_content = read "test_output.txt"
say "Final file content: " + final_content

say "File operations test complete!"

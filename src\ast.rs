use crate::token::DoletType;

/// High-performance AST nodes for Dolet language with Arena Allocation
/// Uses bump allocator for 2x faster parsing and zero fragmentation
#[derive(Debug, Clone, PartialEq)]
#[allow(dead_code)] // Future language features
pub enum Stmt<'arena> {
    // Variable declarations
    VarDecl {
        name: String,
        type_annotation: Option<DoletType>,
        initializer: Option<&'arena Expr<'arena>>,
        is_const: bool,
    },

    // Function declaration
    FunDecl {
        name: String,
        params: Vec<Parameter<'arena>>,
        return_type: Option<DoletType>,
        body: Vec<Stmt<'arena>>,
    },
    
    // Class declaration
    ClassDecl {
        name: String,
        superclass: Option<String>,
        interfaces: Vec<String>,
        members: Vec<ClassMember<'arena>>,
    },

    // Struct declaration
    StructDecl {
        name: String,
        fields: Vec<StructField<'arena>>,
        methods: Vec<Stmt<'arena>>, // FunDecl statements
    },

    // Interface declaration
    InterfaceDecl {
        name: String,
        methods: Vec<MethodSignature<'arena>>,
    },

    // Enum declaration
    EnumDecl {
        name: String,
        variants: Vec<String>,
    },

    // Control flow
    If {
        condition: &'arena Expr<'arena>,
        then_branch: Vec<Stmt<'arena>>,
        else_branch: Option<Vec<Stmt<'arena>>>,
    },
    
    While {
        condition: &'arena Expr<'arena>,
        body: Vec<Stmt<'arena>>,
    },

    For {
        variable: String,
        start: &'arena Expr<'arena>,
        end: &'arena Expr<'arena>,
        body: Vec<Stmt<'arena>>,
    },

    ForIn {
        variable: String,
        iterable: &'arena Expr<'arena>,
        body: Vec<Stmt<'arena>>,
    },

    Match {
        expr: &'arena Expr<'arena>,
        cases: Vec<MatchCase<'arena>>,
        default_case: Option<Vec<Stmt<'arena>>>,
    },

    // Error handling
    Try {
        body: Vec<Stmt<'arena>>,
        catch_clause: Option<CatchClause<'arena>>,
        finally_clause: Option<Vec<Stmt<'arena>>>,
    },

    // Statements
    Expression(&'arena Expr<'arena>),
    Return(Option<&'arena Expr<'arena>>),
    Break,
    Continue,

    // Compound assignment
    CompoundAssign {
        target: String,
        operator: BinaryOp,
        value: &'arena Expr<'arena>,
    },
    
    // I/O operations
    Say(&'arena Expr<'arena>),
    Ask(&'arena Expr<'arena>),

    // File operations
    Write {
        file: &'arena Expr<'arena>,
        content: &'arena Expr<'arena>,
    },
    Append {
        file: &'arena Expr<'arena>,
        content: &'arena Expr<'arena>,
    },

    // Native system calls
    NativeCall {
        function: String,
        args: Vec<&'arena Expr<'arena>>,
    },

    // Macro definition
    MacroDecl {
        name: String,
        params: Vec<String>,
        body: Vec<Stmt<'arena>>,
    },
}

#[derive(Debug, Clone, PartialEq)]
pub enum Expr<'arena> {
    // Literals
    Integer(i64),
    Float(f32),
    Double(f64),
    String(String),
    Char(char),
    Boolean(bool),
    Null,
    
    // Identifiers
    Identifier(String),

    // User input
    Input,

    // File operations
    Read(&'arena Expr<'arena>),
    
    // Binary operations
    Binary {
        left: &'arena Expr<'arena>,
        operator: BinaryOp,
        right: &'arena Expr<'arena>,
    },

    // Unary operations
    Unary {
        operator: UnaryOp,
        operand: &'arena Expr<'arena>,
    },

    // Assignment
    Assign {
        target: &'arena Expr<'arena>,
        value: &'arena Expr<'arena>,
    },

    // Function call
    Call {
        callee: &'arena Expr<'arena>,
        args: Vec<&'arena Expr<'arena>>,
    },
    
    // Member access
    Get {
        object: &'arena Expr<'arena>,
        name: String,
    },

    // Member assignment
    SetProperty {
        object: &'arena Expr<'arena>,
        name: String,
        value: &'arena Expr<'arena>,
    },

    // Array/Map indexing
    Index {
        object: &'arena Expr<'arena>,
        index: &'arena Expr<'arena>,
    },

    // Array literal
    Array(Vec<&'arena Expr<'arena>>),

    // Map literal
    Map(Vec<(&'arena Expr<'arena>, &'arena Expr<'arena>)>),

    // Set literal
    Set(Vec<&'arena Expr<'arena>>),

    // Tuple literal
    Tuple(Vec<&'arena Expr<'arena>>),
    
    // Lambda expression
    Lambda {
        params: Vec<Parameter<'arena>>,
        body: &'arena Expr<'arena>,
    },

    // Type casting
    Cast {
        expr: &'arena Expr<'arena>,
        target_type: DoletType,
    },

    // Conditional expression (ternary)
    Conditional {
        condition: &'arena Expr<'arena>,
        then_expr: &'arena Expr<'arena>,
        else_expr: &'arena Expr<'arena>,
    },
}

#[derive(Debug, Clone, PartialEq)]
pub enum BinaryOp {
    // Arithmetic
    Add, Subtract, Multiply, Divide, Modulo,
    
    // Comparison
    Equal, NotEqual, Less, LessEqual, Greater, GreaterEqual,
    
    // Logical
    And, Or,
    
    // Assignment operators
    AddAssign, SubtractAssign, MultiplyAssign, DivideAssign, ModuloAssign,
}

#[derive(Debug, Clone, PartialEq)]
pub enum UnaryOp {
    Minus, Not,
}

#[derive(Debug, Clone, PartialEq)]
pub struct Parameter<'arena> {
    pub name: String,
    pub param_type: Option<DoletType>,
    pub default_value: Option<&'arena Expr<'arena>>,
}

#[derive(Debug, Clone, PartialEq)]
pub struct StructField<'arena> {
    pub name: String,
    pub field_type: DoletType,
    pub default_value: Option<&'arena Expr<'arena>>,
}

#[derive(Debug, Clone, PartialEq)]
pub struct ClassMember<'arena> {
    pub visibility: Visibility,
    pub is_static: bool,
    pub member: ClassMemberKind<'arena>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ClassMemberKind<'arena> {
    Field {
        name: String,
        field_type: Option<DoletType>,
        initializer: Option<&'arena Expr<'arena>>,
    },
    Method {
        name: String,
        params: Vec<Parameter<'arena>>,
        return_type: Option<DoletType>,
        body: Vec<Stmt<'arena>>,
    },
}

#[derive(Debug, Clone, PartialEq)]
pub enum Visibility {
    Public,
    Private,
}

#[derive(Debug, Clone, PartialEq)]
pub struct MethodSignature<'arena> {
    pub name: String,
    pub params: Vec<Parameter<'arena>>,
    pub return_type: Option<DoletType>,
}

#[derive(Debug, Clone, PartialEq)]
pub struct MatchCase<'arena> {
    pub pattern: &'arena Expr<'arena>,
    pub body: Vec<Stmt<'arena>>,
}

#[derive(Debug, Clone, PartialEq)]
pub struct CatchClause<'arena> {
    pub variable: String,
    pub body: Vec<Stmt<'arena>>,
}

/// Program represents the entire Dolet program
#[derive(Debug, Clone, PartialEq)]
pub struct Program<'arena> {
    pub statements: Vec<Stmt<'arena>>,
    pub imports: Vec<Import>,
}

#[derive(Debug, Clone, PartialEq)]
pub struct Import {
    pub module: String,
    pub alias: Option<String>,
}

impl<'arena> Expr<'arena> {
    /// Get the inferred type of this expression
    /// This is used for compile-time type checking
    pub fn inferred_type(&self) -> DoletType {
        match self {
            Expr::Integer(_) => DoletType::Int,
            Expr::Float(_) => DoletType::Float,
            Expr::Double(_) => DoletType::Double,
            Expr::String(_) => DoletType::String,
            Expr::Char(_) => DoletType::Char,
            Expr::Boolean(_) => DoletType::Bool,
            Expr::Null => DoletType::Null,
            Expr::Array(elements) => {
                if let Some(first) = elements.first() {
                    DoletType::Array(Box::new(first.inferred_type()))
                } else {
                    DoletType::Array(Box::new(DoletType::Unknown))
                }
            }
            Expr::Tuple(elements) => {
                DoletType::Tuple(elements.iter().map(|e| e.inferred_type()).collect())
            }
            _ => DoletType::Unknown, // For complex expressions, type inference happens during semantic analysis
        }
    }
}

impl<'arena> Parameter<'arena> {
    pub fn new(name: String, param_type: Option<DoletType>) -> Self {
        Self {
            name,
            param_type,
            default_value: None,
        }
    }

    pub fn with_default(name: String, param_type: Option<DoletType>, default: &'arena Expr<'arena>) -> Self {
        Self {
            name,
            param_type,
            default_value: Some(default),
        }
    }
}

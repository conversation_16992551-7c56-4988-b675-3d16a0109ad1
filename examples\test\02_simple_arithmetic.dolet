# Simple Arithmetic Operations Test
# Testing basic mathematical operations without mixed types

say "========================================="
say "Simple Arithmetic Test"
say "========================================="

# Integer arithmetic
set a = 10
set b = 3
set c = 0
set d = -5

say "Integer Variables:"
say "a = " + a + ", b = " + b + ", c = " + c + ", d = " + d
say ""

# Addition
set add1 = a + b
set add2 = a + c
set add3 = a + d
set add4 = d + (-3)

say "Addition Operations:"
say a + " + " + b + " = " + add1
say a + " + " + c + " = " + add2
say a + " + " + d + " = " + add3
say d + " + (-3) = " + add4
say ""

# Subtraction
set sub1 = a - b
set sub2 = a - c
set sub3 = a - d
set sub4 = b - a

say "Subtraction Operations:"
say a + " - " + b + " = " + sub1
say a + " - " + c + " = " + sub2
say a + " - " + d + " = " + sub3
say b + " - " + a + " = " + sub4
say ""

# Multiplication
set mul1 = a * b
set mul2 = a * c
set mul3 = a * d
set mul4 = d * (-2)

say "Multiplication Operations:"
say a + " * " + b + " = " + mul1
say a + " * " + c + " = " + mul2
say a + " * " + d + " = " + mul3
say d + " * (-2) = " + mul4
say ""

# Division
set div1 = a / b
set div2 = 15 / 3
set div3 = a / d
set div4 = (-20) / d

say "Division Operations:"
say a + " / " + b + " = " + div1
say "15 / 3 = " + div2
say a + " / " + d + " = " + div3
say "(-20) / " + d + " = " + div4
say ""

# Modulo operations
set mod1 = a % b
set mod2 = 15 % 3
set mod3 = b % a
set mod4 = a % d

say "Modulo Operations:"
say a + " % " + b + " = " + mod1
say "15 % 3 = " + mod2
say b + " % " + a + " = " + mod3
say a + " % " + d + " = " + mod4
say ""

# Float arithmetic (separate)
set fa = 10.5
set fb = 3.2
set fc = 0.0
set fd = -2.7

say "Float Variables:"
say "fa = " + fa + ", fb = " + fb + ", fc = " + fc + ", fd = " + fd
say ""

set float_add = fa + fb
set float_sub = fa - fb
set float_mul = fa * fb
set float_div = fa / fb

say "Float Operations:"
say fa + " + " + fb + " = " + float_add
say fa + " - " + fb + " = " + float_sub
say fa + " * " + fb + " = " + float_mul
say fa + " / " + fb + " = " + float_div
say ""

# Complex expressions (same type)
set complex1 = (a + b) * (a - b)
set complex2 = a * b + c * d
set complex3 = (fa + fb) / (fa - fb)
set complex4 = a + b * c - d

say "Complex Expressions:"
say "(" + a + " + " + b + ") * (" + a + " - " + b + ") = " + complex1
say a + " * " + b + " + " + c + " * " + d + " = " + complex2
say "(" + fa + " + " + fb + ") / (" + fa + " - " + fb + ") = " + complex3
say a + " + " + b + " * " + c + " - " + d + " = " + complex4
say ""

# Large numbers
set large1 = 999999
set large2 = 1000000
set large_add = large1 + large2
set large_mul = large1 * 2

say "Large Numbers:"
say "Large addition: " + large1 + " + " + large2 + " = " + large_add
say "Large multiplication: " + large1 + " * 2 = " + large_mul

say "========================================="
say "Simple Arithmetic Test Complete"
say "========================================="

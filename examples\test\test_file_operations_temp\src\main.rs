use std::io::{self, Write};


fn main() {
    println!("{}", "Testing File Operations...");
    println!("{}", "Test 1: Writing to file");
    std::fs::write("test_output.txt", "Hello, World!").unwrap();
    println!("{}", "Written 'Hello, World!' to test_output.txt");
    std::fs::write("test_output.txt", "This is line 2").unwrap();
    println!("{}", "Written second line to file");
    println!("{}", "Test 2: Reading from file");
    let mut file_content = std::fs::read_to_string("test_output.txt").unwrap_or_default();
    println!("{}", format!("{}{}" , "File content: ", file_content));
    println!("{}", "Test 3: Writing multiple lines");
    std::fs::write("multi_line.txt", "Line 1\nLine 2\nLine 3").unwrap();
    println!("{}", "Written multiple lines to multi_line.txt");
    let mut multi_content = std::fs::read_to_string("multi_line.txt").unwrap_or_default();
    println!("{}", format!("{}{}" , "Multi-line content: ", multi_content));
    println!("{}", "Test 4: Append to file");
    {
        use std::fs::OpenOptions;
        use std::io::Write;
        let mut file = OpenOptions::new().create(true).append(true).open("test_output.txt").unwrap();
        writeln!(file, "{}", "\nAppended line").unwrap();
    }
    println!("{}", "Appended line to file");
    let mut final_content = std::fs::read_to_string("test_output.txt").unwrap_or_default();
    println!("{}", format!("{}{}" , "Final file content: ", final_content));
    println!("{}", "File operations test complete!");
}

use std::io::{self, Write};


fn main() {
    println!("{}", "Testing For Loops...");
    println!("{}", "Test 1: Basic for loop");
    for i in 1..=3 {
        println!("{}", format!("{}{}" , "For loop iteration: ", i));
    }
    println!("{}", "Test 2: Alternative for syntax");
    for j in 0..=2 {
        println!("{}", format!("{}{}" , "Alternative for: ", j));
    }
    println!("{}", "Test 3: For-in loop with array");
    let mut numbers = vec![1, 2, 3];
    for num in numbers {
        println!("{}", format!("{}{}" , "Array item: ", num));
    }
    println!("{}", "For loops test complete!");
}

# Comprehensive Functions Test
# Testing function declarations, calls, parameters, and return values

say "========================================="
say "Functions Test"
say "========================================="

# Simple functions with no parameters
fun greet() -> string:
    return "Hello from function!"
end

fun get_pi() -> float:
    return 3.14159
end

fun get_answer() -> int:
    return 42
end

fun is_ready() -> bool:
    return true
end

say "Simple Functions (No Parameters):"
say "greet() = " + greet()
say "get_pi() = " + get_pi()
say "get_answer() = " + get_answer()
say "is_ready() = " + is_ready()
say ""

# Functions with single parameter
fun square(n: int) -> int:
    return n * n
end

fun double_value(x: float) -> float:
    return x * 2.0
end

fun is_positive(num: int) -> bool:
    return num > 0
end

fun add_prefix(text: string) -> string:
    return "PREFIX_" + text
end

say "Single Parameter Functions:"
say "square(5) = " + square(5)
say "square(-3) = " + square(-3)
say "double_value(2.5) = " + double_value(2.5)
say "is_positive(10) = " + is_positive(10)
say "is_positive(-5) = " + is_positive(-5)
say "add_prefix('test') = " + add_prefix("test")
say ""

# Functions with multiple parameters
fun add(a: int, b: int) -> int:
    return a + b
end

fun multiply(x: float, y: float) -> float:
    return x * y
end

fun max_of_two(a: int, b: int) -> int:
    if a > b:
        return a
    else:
        return b
    end
end

fun concat_with_separator(str1: string, str2: string, sep: string) -> string:
    return str1 + sep + str2
end

fun calculate_area(width: float, height: float) -> float:
    return width * height
end

say "Multiple Parameter Functions:"
say "add(10, 5) = " + add(10, 5)
say "add(-3, 8) = " + add(-3, 8)
say "multiply(2.5, 4.0) = " + multiply(2.5, 4.0)
say "max_of_two(15, 7) = " + max_of_two(15, 7)
say "max_of_two(3, 12) = " + max_of_two(3, 12)
say "concat_with_separator('Hello', 'World', ' ') = " + concat_with_separator("Hello", "World", " ")
say "calculate_area(5.0, 3.0) = " + calculate_area(5.0, 3.0)
say ""

# Functions with mixed parameter types
fun format_person(name: string, age: int, height: float, is_student: bool) -> string:
    return name + " (age: " + age + ", height: " + height + ", student: " + is_student + ")"
end

fun calculate_discount(price: float, discount_percent: int, is_member: bool) -> float:
    set discount = price * discount_percent / 100.0
    if is_member:
        set discount = discount * 1.5
    end
    return price - discount
end

say "Mixed Parameter Types:"
say "format_person('Alice', 25, 5.6, true) = " + format_person("Alice", 25, 5.6, true)
say "calculate_discount(100.0, 10, true) = " + calculate_discount(100.0, 10, true)
say "calculate_discount(100.0, 10, false) = " + calculate_discount(100.0, 10, false)
say ""

# Functions calling other functions
fun circle_area(radius: float) -> float:
    return get_pi() * square_float(radius)
end

fun square_float(n: float) -> float:
    return n * n
end

fun compound_interest(principal: float, rate: float, years: int) -> float:
    set amount = principal
    for i from 1 to years:
        set amount = amount * (1.0 + rate / 100.0)
    end
    return amount
end

say "Functions Calling Functions:"
say "circle_area(3.0) = " + circle_area(3.0)
say "compound_interest(1000.0, 5.0, 3) = " + compound_interest(1000.0, 5.0, 3)
say ""

# Recursive functions
fun factorial(n: int) -> int:
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)
    end
end

fun fibonacci(n: int) -> int:
    if n <= 1:
        return n
    else:
        return fibonacci(n - 1) + fibonacci(n - 2)
    end
end

say "Recursive Functions:"
say "factorial(5) = " + factorial(5)
say "factorial(0) = " + factorial(0)
say "fibonacci(6) = " + fibonacci(6)
say "fibonacci(1) = " + fibonacci(1)
say ""

# Functions with complex logic
fun grade_calculator(score: int) -> string:
    if score >= 90:
        return "A"
    else:
        if score >= 80:
            return "B"
        else:
            if score >= 70:
                return "C"
            else:
                if score >= 60:
                    return "D"
                else:
                    return "F"
                end
            end
        end
    end
end

fun is_prime(n: int) -> bool:
    if n <= 1:
        return false
    end
    if n <= 3:
        return true
    end
    if n % 2 == 0 or n % 3 == 0:
        return false
    end
    set i = 5
    while i * i <= n:
        if n % i == 0 or n % (i + 2) == 0:
            return false
        end
        set i = i + 6
    end
    return true
end

say "Complex Logic Functions:"
say "grade_calculator(95) = " + grade_calculator(95)
say "grade_calculator(75) = " + grade_calculator(75)
say "grade_calculator(55) = " + grade_calculator(55)
say "is_prime(17) = " + is_prime(17)
say "is_prime(15) = " + is_prime(15)
say "is_prime(2) = " + is_prime(2)

say "========================================="
say "Functions Test Complete"
say "========================================="

use std::io::{self, Write};


fn main() {
    println!("{}", "Testing Logical Operators...");
    println!("{}", "Test 1: AND operator (&&)");
    let mut result1 = (true && true);
    println!("{}", format!("{}{}" , "true && true = ", result1));
    let mut result2 = (true && false);
    println!("{}", format!("{}{}" , "true && false = ", result2));
    let mut result3 = (false && false);
    println!("{}", format!("{}{}" , "false && false = ", result3));
    println!("{}", "Test 2: OR operator (||)");
    let mut result4 = (true || false);
    println!("{}", format!("{}{}" , "true || false = ", result4));
    let mut result5 = (false || false);
    println!("{}", format!("{}{}" , "false || false = ", result5));
    let mut result6 = (true || true);
    println!("{}", format!("{}{}" , "true || true = ", result6));
    println!("{}", "Test 3: NOT operator (!)");
    let mut result7 = (!true);
    println!("{}", format!("{}{}" , "!true = ", result7));
    let mut result8 = (!false);
    println!("{}", format!("{}{}" , "!false = ", result8));
    println!("{}", "Test 4: Complex expressions");
    let mut complex1 = ((true && false) || true);
    println!("{}", format!("{}{}" , "(true && false) || true = ", complex1));
    let mut complex2 = (!(true && false));
    println!("{}", format!("{}{}" , "!(true && false) = ", complex2));
    println!("{}", "Logical operators test complete!");
}

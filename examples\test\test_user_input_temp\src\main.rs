use std::io::{self, Write};


fn main() {
    println!("{}", "Testing User Input...");
    println!("{}", "Test 1: Basic user input");
    print!("{}", "What is your name? ");
    io::stdout().flush().unwrap();
    let mut user_name = { let mut input = String::new(); io::stdin().read_line(&mut input).unwrap(); input.trim().to_string() };
    println!("{}", format!("{}{}" , format!("{}{}" , "Hello, ", user_name), "!"));
    println!("{}", "Test 2: Number input");
    print!("{}", "Enter a number: ");
    io::stdout().flush().unwrap();
    let mut user_number = { let mut input = String::new(); io::stdin().read_line(&mut input).unwrap(); input.trim().to_string() };
    println!("{}", format!("{}{}" , "You entered: ", user_number));
    println!("{}", "Test 3: Multiple inputs");
    print!("{}", "Enter your age: ");
    io::stdout().flush().unwrap();
    let mut user_age = { let mut input = String::new(); io::stdin().read_line(&mut input).unwrap(); input.trim().to_string() };
    print!("{}", "Enter your city: ");
    io::stdout().flush().unwrap();
    let mut user_city = { let mut input = String::new(); io::stdin().read_line(&mut input).unwrap(); input.trim().to_string() };
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "You are ", user_age), " years old and live in "), user_city));
    println!("{}", "User input test complete!");
}

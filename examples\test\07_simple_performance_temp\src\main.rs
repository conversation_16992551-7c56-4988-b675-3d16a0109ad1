use std::io::{self, Write};

fn simple_add(a: i64, b: i64) -> i64 {
    return (a + b);
}

fn simple_multiply(a: i64, b: i64) -> i64 {
    return (a * b);
}


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Simple Performance Test");
    println!("{}", "=========================================");
    println!("{}", "Large Number Calculations:");
    let mut large1 = 999999;
    let mut large2 = 888888;
    let mut large3 = 777777;
    let mut large_sum = ((large1 + large2) + large3);
    let mut large_product = (large1 * 2);
    let mut large_division = (large1 / 3);
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Sum: ", large1), " + "), large2), " + "), large3), " = "), large_sum));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "Product: ", large1), " * 2 = "), large_product));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "Division: ", large1), " / 3 = "), large_division));
    println!("{}", "");
    println!("{}", "Loop Performance Test:");
    let mut counter = 0;
    let mut sum = 0;
    while (counter < 100) {
        sum = (sum + counter);
        counter = (counter + 1);
    }
    println!("{}", format!("{}{}" , "Sum of numbers 0 to 99: ", sum));
    println!("{}", "");
    println!("{}", "Array Processing Performance:");
    let mut numbers = vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    let mut array_sum = 0;
    let mut i = 0;
    while (i < 10) {
        array_sum = (array_sum + numbers[i as usize]);
        i = (i + 1);
    }
    println!("{}", format!("{}{}" , "Array sum: ", array_sum));
    println!("{}", "");
    println!("{}", "Mathematical Operations Performance:");
    let mut math_result = 0;
    let mut operations = 0;
    while (operations < 50) {
        temp_calc = (operations * 2);
        temp_div = (operations / 2);
        temp_mod = (operations % 3);
        math_result = (((math_result + temp_calc) + temp_div) - temp_mod);
        operations = (operations + 1);
    }
    println!("{}", format!("{}{}" , "Complex math operations result: ", math_result));
    println!("{}", "");
    println!("{}", "Function Call Performance:");
    let mut func_result1 = simple_add(100, 200);
    let mut func_result2 = simple_multiply(25, 4);
    let mut func_result3 = simple_add(func_result1, func_result2);
    println!("{}", format!("{}{}" , "simple_add(100, 200) = ", func_result1));
    println!("{}", format!("{}{}" , "simple_multiply(25, 4) = ", func_result2));
    println!("{}", format!("{}{}" , "Combined result = ", func_result3));
    println!("{}", "");
    println!("{}", "Boolean Operations Performance:");
    let mut bool_operations = 0;
    let mut bool_result = true;
    while (bool_operations < 100) {
        is_even = ((bool_operations % 2) == 0);
        is_divisible_by_3 = ((bool_operations % 3) == 0);
        bool_result = (bool_result && is_even);
        bool_result = (bool_result || is_divisible_by_3);
        bool_operations = (bool_operations + 1);
    }
    println!("{}", format!("{}{}" , "Boolean operations completed, result: ", bool_result));
    println!("{}", "");
    println!("{}", "Array Operations Performance:");
    let mut perf_array = vec![10, 20, 30, 40, 50];
    let mut array_operations = 0;
    let mut array_result = 0;
    while (array_operations < 5) {
        doubled = (perf_array[array_operations as usize] * 2);
        array_result = (array_result + doubled);
        array_operations = (array_operations + 1);
    }
    println!("{}", format!("{}{}" , "Array operations result: ", array_result));
    println!("{}", "");
    println!("{}", "Comparison Operations Performance:");
    let mut comparisons = 0;
    let mut comparison_results = 0;
    while (comparisons < 100) {
        if (comparisons > 50) {
            comparison_results = (comparison_results + 1);
        }
        if ((comparisons % 10) == 0) {
            comparison_results = (comparison_results + 2);
        }
        comparisons = (comparisons + 1);
    }
    println!("{}", format!("{}{}" , "Comparison operations result: ", comparison_results));
    println!("{}", "");
    println!("{}", "Nested Calculations:");
    let mut nested_result = 0;
    let mut outer = 0;
    while (outer < 10) {
        inner = 0;
        // Unsupported statement in while loop
        outer = (outer + 1);
    }
    println!("{}", format!("{}{}" , "Nested calculation result: ", nested_result));
    println!("{}", "=========================================");
    println!("{}", "Simple Performance Test Complete");
    println!("{}", "=========================================");
}

use std::io::{self, Write};

fn greet() -> String {
    return "Hello from function!".to_string();
}

fn get_pi() -> f32 {
    return 3.14159f32;
}

fn get_answer() -> i64 {
    return 42;
}

fn is_ready() -> bool {
    return true;
}

fn square(n: i64) -> i64 {
    return (n * n);
}

fn double_value(x: f32) -> f32 {
    return (x as f32 * 2f32 as f32);
}

fn is_positive(num: i64) -> bool {
    return (num > 0);
}

fn add(a: i64, b: i64) -> i64 {
    return (a + b);
}

fn multiply(x: f32, y: f32) -> f32 {
    return (x as f32 * y as f32);
}

fn max_of_two(a: i64, b: i64) -> i64 {
    if (a > b) {
        return a;
    } else {
        return b;
    }
    0
}

fn circle_area(radius: f32) -> f32 {
    return (get_pi() as f32 * square_float(radius) as f32);
}

fn square_float(n: f32) -> f32 {
    return (n * n);
}

fn factorial(n: i64) -> i64 {
    if (n <= 1) {
        return 1;
    } else {
        return (n * factorial((n - 1)));
    }
    0
}

fn grade_calculator(score: i64) -> String {
    if (score >= 90) {
        return "A".to_string();
    } else {
        if (score >= 80) {
            return "B".to_string();
        } else {
            // Unsupported nested statement
        }
    }
    String::new()
}

fn is_even(n: i64) -> bool {
    return ((n % 2) == 0);
}


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Simple Functions Test");
    println!("{}", "=========================================");
    println!("{}", "Simple Functions (No Parameters):");
    println!("{}", format!("{}{}" , "greet() = ", greet()));
    println!("{}", format!("{}{}" , "get_pi() = ", get_pi()));
    println!("{}", format!("{}{}" , "get_answer() = ", get_answer()));
    println!("{}", format!("{}{}" , "is_ready() = ", is_ready()));
    println!("{}", "");
    println!("{}", "Single Parameter Functions:");
    println!("{}", format!("{}{}" , "square(5) = ", square(5)));
    println!("{}", format!("{}{}" , "square(-3) = ", square((-3))));
    println!("{}", format!("{}{}" , "double_value(2.5) = ", double_value(2.5f32)));
    println!("{}", format!("{}{}" , "is_positive(10) = ", is_positive(10)));
    println!("{}", format!("{}{}" , "is_positive(-5) = ", is_positive((-5))));
    println!("{}", "");
    println!("{}", "Multiple Parameter Functions:");
    println!("{}", format!("{}{}" , "add(10, 5) = ", add(10, 5)));
    println!("{}", format!("{}{}" , "add(-3, 8) = ", add((-3), 8)));
    println!("{}", format!("{}{}" , "multiply(2.5, 4.0) = ", multiply(2.5f32, 4f32)));
    println!("{}", format!("{}{}" , "max_of_two(15, 7) = ", max_of_two(15, 7)));
    println!("{}", format!("{}{}" , "max_of_two(3, 12) = ", max_of_two(3, 12)));
    println!("{}", "");
    println!("{}", "Functions Calling Functions:");
    println!("{}", format!("{}{}" , "circle_area(3.0) = ", circle_area(3f32)));
    println!("{}", "");
    println!("{}", "Recursive Functions:");
    println!("{}", format!("{}{}" , "factorial(5) = ", factorial(5)));
    println!("{}", format!("{}{}" , "factorial(0) = ", factorial(0)));
    println!("{}", format!("{}{}" , "factorial(3) = ", factorial(3)));
    println!("{}", "");
    println!("{}", "Logic Functions:");
    println!("{}", format!("{}{}" , "grade_calculator(95) = ", grade_calculator(95)));
    println!("{}", format!("{}{}" , "grade_calculator(75) = ", grade_calculator(75)));
    println!("{}", format!("{}{}" , "grade_calculator(55) = ", grade_calculator(55)));
    println!("{}", format!("{}{}" , "is_even(10) = ", is_even(10)));
    println!("{}", format!("{}{}" , "is_even(7) = ", is_even(7)));
    println!("{}", "");
    let mut result1 = add(5, 3);
    let mut result2 = multiply(2f32, 3.5f32);
    let mut result3 = is_positive(result1);
    println!("{}", "Function Results in Variables:");
    println!("{}", format!("{}{}" , "result1 = add(5, 3) = ", result1));
    println!("{}", format!("{}{}" , "result2 = multiply(2.0, 3.5) = ", result2));
    println!("{}", format!("{}{}" , "result3 = is_positive(result1) = ", result3));
    println!("{}", "=========================================");
    println!("{}", "Simple Functions Test Complete");
    println!("{}", "=========================================");
}

use std::io::{self, Write};

fn fibonacci_iterative(n: i64) -> i64 {
    if (n <= 1) {
        return n;
    }
    let mut a = 0;
    let mut b = 1;
    let mut i = 2;
    while (i <= n) {
        temp = (a + b);
        a = b;
        b = temp;
        i = (i + 1);
    }
    return b;
}


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Performance Test");
    println!("{}", "=========================================");
    println!("{}", "Large Number Calculations:");
    let mut large1 = 999999;
    let mut large2 = 888888;
    let mut large3 = 777777;
    let mut large_sum = ((large1 + large2) + large3);
    let mut large_product = (large1 * 2);
    let mut large_division = (large1 / 3);
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Sum: ", large1), " + "), large2), " + "), large3), " = "), large_sum));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "Product: ", large1), " * 2 = "), large_product));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "Division: ", large1), " / 3 = "), large_division));
    println!("{}", "");
    println!("{}", "Loop Performance Test:");
    let mut counter = 0;
    let mut sum = 0;
    while (counter < 100) {
        sum = (sum + counter);
        counter = (counter + 1);
    }
    println!("{}", format!("{}{}" , "Sum of numbers 0 to 99: ", sum));
    println!("{}", "");
    println!("{}", "Nested Loop Test:");
    let mut total = 0;
    for i in 1..=10 {
        // Unsupported statement in for loop
    }
    println!("{}", format!("{}{}" , "Total iterations (10x10): ", total));
    println!("{}", "");
    println!("{}", "Array Processing Performance:");
    let mut numbers = vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    let mut array_sum = 0;
    let mut i = 0;
    while (i < 10) {
        array_sum = (array_sum + numbers[i as usize]);
        i = (i + 1);
    }
    println!("{}", format!("{}{}" , "Array sum: ", array_sum));
    println!("{}", "");
    println!("{}", "String Processing Performance:");
    let mut base_string = "Performance";
    let mut result_string = "";
    let mut count = 0;
    while (count < 10) {
        result_string = format!("{}{}" , (result_string + base_string), " ");
        count = (count + 1);
    }
    println!("{}", "Concatenated string length test completed");
    println!("{}", "");
    println!("{}", "Mathematical Operations Performance:");
    let mut math_result = 0;
    let mut operations = 0;
    while (operations < 50) {
        math_result = (((math_result + (operations * 2)) + (operations / 2)) - (operations % 3));
        operations = (operations + 1);
    }
    println!("{}", format!("{}{}" , "Complex math operations result: ", math_result));
    println!("{}", "");
    println!("{}", "Function Call Performance:");
    println!("{}", format!("{}{}" , "fibonacci_iterative(10) = ", fibonacci_iterative(10)));
    println!("{}", format!("{}{}" , "fibonacci_iterative(15) = ", fibonacci_iterative(15)));
    println!("{}", format!("{}{}" , "fibonacci_iterative(20) = ", fibonacci_iterative(20)));
    println!("{}", "");
    println!("{}", "Boolean Operations Performance:");
    let mut bool_operations = 0;
    let mut bool_result = true;
    while (bool_operations < 100) {
        bool_result = (bool_result && ((bool_operations % 2) == 0));
        bool_result = (bool_result || ((bool_operations % 3) == 0));
        bool_operations = (bool_operations + 1);
    }
    println!("{}", format!("{}{}" , "Boolean operations completed, result: ", bool_result));
    println!("{}", "");
    println!("{}", "Array Operations Performance:");
    let mut perf_array = vec![10, 20, 30, 40, 50];
    let mut array_operations = 0;
    let mut array_result = 0;
    while (array_operations < 5) {
        array_result = (array_result + (perf_array[array_operations as usize] * 2));
        array_operations = (array_operations + 1);
    }
    println!("{}", format!("{}{}" , "Array operations result: ", array_result));
    println!("{}", "");
    println!("{}", "Comparison Operations Performance:");
    let mut comparisons = 0;
    let mut comparison_results = 0;
    while (comparisons < 100) {
        if (comparisons > 50) {
            comparison_results = (comparison_results + 1);
        }
        if ((comparisons % 10) == 0) {
            comparison_results = (comparison_results + 2);
        }
        comparisons = (comparisons + 1);
    }
    println!("{}", format!("{}{}" , "Comparison operations result: ", comparison_results));
    println!("{}", "=========================================");
    println!("{}", "Performance Test Complete");
    println!("{}", "=========================================");
}

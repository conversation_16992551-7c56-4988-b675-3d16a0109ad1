# Basic String Test
# Testing string operations that work with current compiler

say "========================================="
say "Basic String Test"
say "========================================="

# Basic string variables
set str1 = "Hello"
set str2 = "World"
set str3 = ""
set str4 = " "
set str5 = "123"

say "Basic String Variables:"
say "str1 = " + str1
say "str2 = " + str2
say "str3 = '" + str3 + "'"
say "str4 = '" + str4 + "'"
say "str5 = " + str5
say ""

# String concatenation in say statements (this works)
say "String Concatenation:"
say "Simple: " + str1 + " " + str2
say "Empty: " + str1 + str3 + str2
say "Space: " + str1 + str4 + str2
say "Numbers: " + str5 + " test"
say ""

# String with numbers and booleans
set num1 = 42
set num2 = 3
set bool1 = true
set bool2 = false

say "String with Other Types:"
say "Number: " + num1
say "Another number: " + num2
say "Boolean true: " + bool1
say "Boolean false: " + bool2
say "Null value: " + null
say ""

# Complex concatenations in say
say "Complex Concatenations:"
say "Values: " + num1 + ", " + num2 + ", " + bool1
say "Math: " + num1 + " + " + num2 + " = " + (num1 + num2)
say "Comparison: " + num1 + " > " + num2 + " is " + (num1 > num2)
say "Text and numbers: " + str1 + " " + num1 + " " + str2
say ""

# Empty and space handling
say "Empty and Space Handling:"
say "Before" + str3 + "After"
say "Before" + str4 + "After"
say "[" + str3 + "]"
say "[" + str4 + "]"
say ""

# Long strings
set long_str = "This is a longer string for testing the string handling capabilities"
say "Long String:"
say long_str
say "Prefix: " + long_str
say long_str + " :Suffix"
say ""

# Special characters (simple ones)
set special1 = "Test!@#$%"
set special2 = "Numbers123"
set special3 = "Spaces   Here"
set special4 = "Symbols()[]{}+-*/"

say "Special Characters:"
say special1
say special2
say special3
say special4
say "Combined: " + special1 + " " + special2
say ""

# Multiple data types
say "Multiple Data Types:"
say "All types: " + str1 + ", " + num1 + ", " + 3.14 + ", " + bool1 + ", " + null
say "Expression: " + str1 + " has " + (5 * 2) + " letters times " + 2.5
say ""

# Repeated patterns
say "Repeated Patterns:"
say "Single: " + str1
say "Double: " + str1 + str1
say "Triple: " + str1 + str1 + str1
say "With spaces: " + str1 + " " + str1 + " " + str1
say ""

# Edge cases
say "Edge Cases:"
say str3 + "Empty start"
say "Empty end" + str3
say str3 + str3 + "Double empty"
say str4 + str4 + str4 + "Triple space"

say "========================================="
say "Basic String Test Complete"
say "========================================="

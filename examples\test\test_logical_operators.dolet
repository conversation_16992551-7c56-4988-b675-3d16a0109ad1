# Test Logical Operators - Check if they work
say "Testing Logical Operators..."

# Test 1: AND operator
say "Test 1: AND operator (&&)"
set result1 = true && true
say "true && true = " + result1

set result2 = true && false
say "true && false = " + result2

set result3 = false && false
say "false && false = " + result3

# Test 2: OR operator
say "Test 2: OR operator (||)"
set result4 = true || false
say "true || false = " + result4

set result5 = false || false
say "false || false = " + result5

set result6 = true || true
say "true || true = " + result6

# Test 3: NOT operator
say "Test 3: NOT operator (!)"
set result7 = !true
say "!true = " + result7

set result8 = !false
say "!false = " + result8

# Test 4: Complex logical expressions
say "Test 4: Complex expressions"
set complex1 = (true && false) || true
say "(true && false) || true = " + complex1

set complex2 = !(true && false)
say "!(true && false) = " + complex2

say "Logical operators test complete!"

use std::io::{self, Write};

fn sum_two(x: i64, y: i64) -> i64 {
    return (x as f32 + y as f32);
}

fn concat_strings(s1: &str, s2: &str) -> String {
    return format!("{}{}" , format!("{}{}" , s1, " "), s2).to_string();
}


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Simple Arrays Test");
    println!("{}", "=========================================");
    let mut numbers = vec![1, 2, 3, 4, 5];
    let mut strings = vec!["hello", "world", "test"];
    let mut booleans = vec![true, false, true];
    println!("{}", "Basic Arrays (individual access):");
    println!("{}", format!("{}{}" , "numbers[0] = ", numbers[0 as usize]));
    println!("{}", format!("{}{}" , "numbers[1] = ", numbers[1 as usize]));
    println!("{}", format!("{}{}" , "numbers[2] = ", numbers[2 as usize]));
    println!("{}", format!("{}{}" , "numbers[3] = ", numbers[3 as usize]));
    println!("{}", format!("{}{}" , "numbers[4] = ", numbers[4 as usize]));
    println!("{}", "");
    println!("{}", "String Array Access:");
    println!("{}", format!("{}{}" , "strings[0] = ", strings[0 as usize]));
    println!("{}", format!("{}{}" , "strings[1] = ", strings[1 as usize]));
    println!("{}", format!("{}{}" , "strings[2] = ", strings[2 as usize]));
    println!("{}", "");
    println!("{}", "Boolean Array Access:");
    println!("{}", format!("{}{}" , "booleans[0] = ", booleans[0 as usize]));
    println!("{}", format!("{}{}" , "booleans[1] = ", booleans[1 as usize]));
    println!("{}", format!("{}{}" , "booleans[2] = ", booleans[2 as usize]));
    println!("{}", "");
    println!("{}", "Array Operations:");
    let mut first_num = numbers[0 as usize];
    let mut second_num = numbers[1 as usize];
    let mut third_num = numbers[2 as usize];
    let mut sum = ((first_num + second_num) + third_num);
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "First three numbers: ", first_num), ", "), second_num), ", "), third_num));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Sum: ", first_num), " + "), second_num), " + "), third_num), " = "), sum));
    println!("{}", "");
    let mut calculated = vec![(1 + 1), (2 * 3), (10 / 2), (15 % 4)];
    println!("{}", "Calculated Array:");
    println!("{}", format!("{}{}" , "calculated[0] = 1 + 1 = ", calculated[0 as usize]));
    println!("{}", format!("{}{}" , "calculated[1] = 2 * 3 = ", calculated[1 as usize]));
    println!("{}", format!("{}{}" , "calculated[2] = 10 / 2 = ", calculated[2 as usize]));
    println!("{}", format!("{}{}" , "calculated[3] = 15 % 4 = ", calculated[3 as usize]));
    println!("{}", "");
    let mut a = 10;
    let mut b = 20;
    let mut c = 30;
    let mut var_array = vec![a, b, c];
    println!("{}", "Array with Variables:");
    println!("{}", format!("{}{}" , "var_array[0] = ", var_array[0 as usize]));
    println!("{}", format!("{}{}" , "var_array[1] = ", var_array[1 as usize]));
    println!("{}", format!("{}{}" , "var_array[2] = ", var_array[2 as usize]));
    println!("{}", "");
    println!("{}", "Array Comparisons:");
    println!("{}", format!("{}{}" , "numbers[0] < numbers[1]: ", (numbers[0 as usize] < numbers[1 as usize])));
    println!("{}", format!("{}{}" , "numbers[4] > numbers[0]: ", (numbers[4 as usize] > numbers[0 as usize])));
    println!("{}", format!("{}{}" , "strings[0] == strings[0]: ", (strings[0 as usize] == strings[0 as usize])));
    println!("{}", format!("{}{}" , "booleans[0] != booleans[1]: ", (booleans[0 as usize] != booleans[1 as usize])));
    println!("{}", "");
    println!("{}", "Array Elements in Functions:");
    println!("{}", format!("{}{}" , "sum_two(numbers[0], numbers[1]) = ", sum_two(numbers[0 as usize], numbers[1 as usize])));
    println!("{}", format!("{}{}" , "concat_strings(strings[0], strings[1]) = ", concat_strings(strings[0 as usize], strings[1 as usize])));
    println!("{}", "");
    let mut empty_nums = vec![0i64; 0];
    println!("{}", "Empty Array Test:");
    println!("{}", "Empty array created");
    let mut single = vec![42];
    let mut pair = vec![1, 2];
    let mut triple = vec![10, 20, 30];
    println!("{}", "Different Array Sizes:");
    println!("{}", format!("{}{}" , "single[0] = ", single[0 as usize]));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "pair[0] = ", pair[0 as usize]), ", pair[1] = "), pair[1 as usize]));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "triple[0] = ", triple[0 as usize]), ", triple[1] = "), triple[1 as usize]), ", triple[2] = "), triple[2 as usize]));
    println!("{}", "=========================================");
    println!("{}", "Simple Arrays Test Complete");
    println!("{}", "=========================================");
}

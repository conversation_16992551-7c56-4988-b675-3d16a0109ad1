# Test Advanced Features - Check if they work
say "Testing Advanced Features..."

# Test 1: Multiple assignments
say "Test 1: Multiple assignments"
set x, y, z = 1, 2, 3
say "x = " + x + ", y = " + y + ", z = " + z

# Test 2: Ternary operator
say "Test 2: Ternary operator"
set age = 20
set status = age >= 18 ? "adult" : "minor"
say "Status: " + status

# Test 3: Default parameters
say "Test 3: Default parameters"
fun greet_with_title(name, title = "Mr."):
    say title + " " + name
end

greet_with_title("<PERSON>")
greet_with_title("<PERSON>", "<PERSON>.")

# Test 4: Variable arguments
say "Test 4: Variable arguments"
fun sum_all(...numbers):
    set total = 0
    for num in numbers:
        set total = total + num
    end
    return total
end

set result = sum_all(1, 2, 3, 4, 5)
say "Sum of 1,2,3,4,5 = " + result

# Test 5: Lambda functions
say "Test 5: Lambda functions"
set square = lambda x: x * x
set squared = square(5)
say "square(5) = " + squared

# Test 6: List comprehensions
say "Test 6: List comprehensions"
set numbers = [1, 2, 3, 4, 5]
set squares = [x * x for x in numbers]
say "Squares: " + squares

say "Advanced features test complete!"

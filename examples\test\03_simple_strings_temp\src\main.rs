use std::io::{self, Write};


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Simple String Test");
    println!("{}", "=========================================");
    let mut str1 = "Hello";
    let mut str2 = "World";
    let mut str3 = "";
    let mut str4 = " ";
    let mut str5 = "123";
    println!("{}", "Basic String Variables:");
    println!("{}", format!("{}{}" , "str1 = ", str1));
    println!("{}", format!("{}{}" , "str2 = ", str2));
    println!("{}", format!("{}{}" , format!("{}{}" , "str3 = '", str3), "'"));
    println!("{}", format!("{}{}" , format!("{}{}" , "str4 = '", str4), "'"));
    println!("{}", format!("{}{}" , "str5 = ", str5));
    println!("{}", "");
    println!("{}", "String Concatenation with say:");
    println!("{}", format!("{}{}" , format!("{}{}" , str1, " "), str2));
    println!("{}", (str1 + str2));
    println!("{}", ((str1 + str3) + str2));
    println!("{}", ((str3 + str1) + str3));
    println!("{}", "");
    let mut num1 = 42;
    let mut num2 = 3;
    let mut bool1 = true;
    println!("{}", "String with Numbers and Booleans:");
    println!("{}", format!("{}{}" , "Number: ", num1));
    println!("{}", format!("{}{}" , "Another number: ", num2));
    println!("{}", format!("{}{}" , "Boolean: ", bool1));
    println!("{}", format!("{}{}" , "Sum: ", (num1 + num2)));
    println!("{}", "");
    println!("{}", "Multiple Concatenations:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Values: ", num1), ", "), num2), ", "), bool1));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , str1, " "), str2), " "), num1));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "Result: ", (num1 + num2)), " is "), (num1 > num2)));
    println!("{}", "");
    println!("{}", "Empty and Space Strings:");
    println!("{}", format!("{}{}" , format!("{}{}" , "Before", str3), "After"));
    println!("{}", format!("{}{}" , format!("{}{}" , "Before", str4), "After"));
    println!("{}", format!("{}{}" , format!("{}{}" , "[", str3), "]"));
    println!("{}", format!("{}{}" , format!("{}{}" , "[", str4), "]"));
    println!("{}", "");
    let mut long_str = "This is a longer string for testing purposes";
    println!("{}", "Long String:");
    println!("{}", long_str);
    println!("{}", format!("{}{}" , format!("{}{}" , "Length test: ", long_str), " - complete"));
    println!("{}", "");
    let mut special1 = "Test!@#$%";
    let mut special2 = "Numbers123";
    let mut special3 = "Spaces   Here";
    println!("{}", "Special Characters:");
    println!("{}", special1);
    println!("{}", special2);
    println!("{}", special3);
    println!("{}", "");
    let mut repeat_base = "Test";
    println!("{}", "Repeated Concatenation:");
    println!("{}", repeat_base);
    println!("{}", (repeat_base + repeat_base));
    println!("{}", ((repeat_base + repeat_base) + repeat_base));
    println!("{}", "");
    println!("{}", "Mixed Data Types:");
    println!("{}", format!("{}{}" , "Integer: ", 100));
    println!("{}", format!("{}{}" , "Float: ", 3.14f32));
    println!("{}", format!("{}{}" , "Boolean: ", false));
    println!("{}", format!("{}{}" , "Null: ", "null"));
    println!("{}", "=========================================");
    println!("{}", "Simple String Test Complete");
    println!("{}", "=========================================");
}

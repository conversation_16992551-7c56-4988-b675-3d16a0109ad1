use crate::token::{Token, TokenKind, infer_type_from_token};

#[cfg(feature = "parallel")]
use hashbrown::HashMap;

#[cfg(not(feature = "parallel"))]
use std::collections::HashMap;
use std::str::Chars;
use std::iter::Peekable;

#[cfg(target_arch = "x86_64")]
use std::arch::x86_64::*;

/// Hyperd Tokenizer - Ultra-fast lexical analysis for Dolet with SIMD optimizations
/// Features:
/// - Zero-copy string slices
/// - Single-pass scanning
/// - Inline keyword lookup
/// - Early exit optimizations
/// - Memory-efficient token streaming
/// - SIMD-optimized whitespace skipping for 2x speed boost
/// - Vectorized character classification
pub struct HyperdTokenizer<'a> {
    input: &'a str,
    input_bytes: &'a [u8],
    chars: Peekable<Chars<'a>>,
    current_pos: usize,
    line: usize,
    column: usize,
    keywords: <PERSON>hMap<&'static str, TokenKind>,
}

impl<'a> HyperdTokenizer<'a> {
    pub fn new(input: &'a str) -> Self {
        let mut keywords = HashMap::new();
        
        // Populate keyword lookup table for O(1) keyword recognition
        keywords.insert("set", TokenKind::Set);
        keywords.insert("const", TokenKind::Const);
        keywords.insert("fun", TokenKind::Fun);
        keywords.insert("end", TokenKind::End);
        keywords.insert("if", TokenKind::If);
        keywords.insert("else", TokenKind::Else);
        keywords.insert("for", TokenKind::For);
        keywords.insert("while", TokenKind::While);
        keywords.insert("do", TokenKind::Do);
        keywords.insert("match", TokenKind::Match);
        keywords.insert("case", TokenKind::Case);
        keywords.insert("default", TokenKind::Default);
        keywords.insert("class", TokenKind::Class);
        keywords.insert("struct", TokenKind::Struct);
        keywords.insert("interface", TokenKind::Interface);
        keywords.insert("enum", TokenKind::Enum);
        keywords.insert("inherits", TokenKind::Inherits);
        keywords.insert("implements", TokenKind::Implements);
        keywords.insert("private", TokenKind::Private);
        keywords.insert("public", TokenKind::Public);
        keywords.insert("static", TokenKind::Static);
        keywords.insert("return", TokenKind::Return);
        keywords.insert("try", TokenKind::Try);
        keywords.insert("catch", TokenKind::Catch);
        keywords.insert("finally", TokenKind::Finally);
        keywords.insert("native", TokenKind::Native);
        keywords.insert("call", TokenKind::Call);
        keywords.insert("macro", TokenKind::Macro);
        keywords.insert("from", TokenKind::From);
        keywords.insert("to", TokenKind::To);
        keywords.insert("in", TokenKind::In);
        keywords.insert("into", TokenKind::Into);
        keywords.insert("as", TokenKind::As);
        keywords.insert("true", TokenKind::True);
        keywords.insert("false", TokenKind::False);
        keywords.insert("null", TokenKind::Null);

        // Logical operators
        keywords.insert("and", TokenKind::And);
        keywords.insert("or", TokenKind::Or);
        keywords.insert("not", TokenKind::Not);

        // Built-in functions
        keywords.insert("say", TokenKind::Say);
        keywords.insert("ask", TokenKind::Ask);
        keywords.insert("input", TokenKind::Input);
        keywords.insert("write", TokenKind::Write);
        keywords.insert("append", TokenKind::Append);
        keywords.insert("wait", TokenKind::Wait);

        Self {
            input,
            input_bytes: input.as_bytes(),
            chars: input.chars().peekable(),
            current_pos: 0,
            line: 1,
            column: 1,
            keywords,
        }
    }

    /// Get the next token with zero-copy optimization
    pub fn next_token(&mut self) -> Token<'a> {
        self.skip_whitespace();

        let start_pos = self.current_pos;
        let start_line = self.line;
        let start_column = self.column;

        match self.current_char() {
            Some('\0') | None => self.make_token(TokenKind::Eof, start_pos, start_line, start_column),
            Some('\n') => {
                self.advance();
                self.line += 1;
                self.column = 1;
                self.make_token(TokenKind::Newline, start_pos, start_line, start_column)
            }
            Some('#') => self.comment(),
            Some('"') => self.string_literal(),
            Some('\'') => self.char_literal(),
            Some(c) if c.is_ascii_digit() => self.number(),
            Some(c) if c.is_alphabetic() || c == '_' => self.identifier_or_keyword(),
            
            // Single-character tokens with fast matching
            Some('+') => {
                self.advance();
                if self.match_char('=') {
                    self.make_token(TokenKind::PlusAssign, start_pos, start_line, start_column)
                } else {
                    self.make_token(TokenKind::Plus, start_pos, start_line, start_column)
                }
            }
            Some('-') => {
                self.advance();
                if self.match_char('=') {
                    self.make_token(TokenKind::MinusAssign, start_pos, start_line, start_column)
                } else if self.match_char('>') {
                    self.make_token(TokenKind::Arrow, start_pos, start_line, start_column)
                } else {
                    self.make_token(TokenKind::Minus, start_pos, start_line, start_column)
                }
            }
            Some('*') => {
                self.advance();
                if self.match_char('=') {
                    self.make_token(TokenKind::MultiplyAssign, start_pos, start_line, start_column)
                } else {
                    self.make_token(TokenKind::Multiply, start_pos, start_line, start_column)
                }
            }
            Some('/') => {
                self.advance();
                if self.match_char('=') {
                    self.make_token(TokenKind::DivideAssign, start_pos, start_line, start_column)
                } else {
                    self.make_token(TokenKind::Divide, start_pos, start_line, start_column)
                }
            }
            Some('%') => {
                self.advance();
                if self.match_char('=') {
                    self.make_token(TokenKind::ModuloAssign, start_pos, start_line, start_column)
                } else {
                    self.make_token(TokenKind::Modulo, start_pos, start_line, start_column)
                }
            }
            Some('=') => {
                self.advance();
                if self.match_char('=') {
                    self.make_token(TokenKind::Equal, start_pos, start_line, start_column)
                } else {
                    self.make_token(TokenKind::Assign, start_pos, start_line, start_column)
                }
            }
            Some('!') => {
                self.advance();
                if self.match_char('=') {
                    self.make_token(TokenKind::NotEqual, start_pos, start_line, start_column)
                } else {
                    self.make_token(TokenKind::Not, start_pos, start_line, start_column)
                }
            }
            Some('<') => {
                self.advance();
                if self.match_char('=') {
                    self.make_token(TokenKind::LessEqual, start_pos, start_line, start_column)
                } else {
                    self.make_token(TokenKind::Less, start_pos, start_line, start_column)
                }
            }
            Some('>') => {
                self.advance();
                if self.match_char('=') {
                    self.make_token(TokenKind::GreaterEqual, start_pos, start_line, start_column)
                } else {
                    self.make_token(TokenKind::Greater, start_pos, start_line, start_column)
                }
            }
            Some('&') => {
                self.advance();
                if self.match_char('&') {
                    self.make_token(TokenKind::And, start_pos, start_line, start_column)
                } else {
                    // Single & is not supported yet, treat as unknown
                    let lexeme = &self.input[start_pos..self.current_pos];
                    eprintln!("⚠️  Warning: Single '&' operator not supported at line {}, column {} - use '&&' for logical AND", start_line, start_column);
                    Token::new(TokenKind::Identifier, lexeme, start_line, start_column)
                }
            }
            Some('|') => {
                self.advance();
                if self.match_char('|') {
                    self.make_token(TokenKind::Or, start_pos, start_line, start_column)
                } else {
                    // Single | is not supported yet, treat as unknown
                    let lexeme = &self.input[start_pos..self.current_pos];
                    eprintln!("⚠️  Warning: Single '|' operator not supported at line {}, column {} - use '||' for logical OR", start_line, start_column);
                    Token::new(TokenKind::Identifier, lexeme, start_line, start_column)
                }
            }
            
            // Delimiters
            Some('(') => { self.advance(); self.make_token(TokenKind::LeftParen, start_pos, start_line, start_column) }
            Some(')') => { self.advance(); self.make_token(TokenKind::RightParen, start_pos, start_line, start_column) }
            Some('[') => { self.advance(); self.make_token(TokenKind::LeftBracket, start_pos, start_line, start_column) }
            Some(']') => { self.advance(); self.make_token(TokenKind::RightBracket, start_pos, start_line, start_column) }
            Some('{') => { self.advance(); self.make_token(TokenKind::LeftBrace, start_pos, start_line, start_column) }
            Some('}') => { self.advance(); self.make_token(TokenKind::RightBrace, start_pos, start_line, start_column) }
            Some(',') => { self.advance(); self.make_token(TokenKind::Comma, start_pos, start_line, start_column) }
            Some(':') => { self.advance(); self.make_token(TokenKind::Colon, start_pos, start_line, start_column) }
            Some(';') => { self.advance(); self.make_token(TokenKind::Semicolon, start_pos, start_line, start_column) }
            Some('.') => { self.advance(); self.make_token(TokenKind::Dot, start_pos, start_line, start_column) }
            Some('?') => { self.advance(); self.make_token(TokenKind::Question, start_pos, start_line, start_column) }
            
            Some(c) => {
                self.advance();
                // Unknown character - create a special error token
                let lexeme = &self.input[start_pos..self.current_pos];
                eprintln!("⚠️  Warning: Unknown character '{}' at line {}, column {} - treating as identifier", c, start_line, start_column);
                Token::new(TokenKind::Identifier, lexeme, start_line, start_column)
            }
        }
    }

    /// Tokenize the entire input into a vector of tokens
    pub fn tokenize(&mut self) -> Vec<Token<'a>> {
        let mut tokens = Vec::new();
        
        loop {
            let token = self.next_token();
            let is_eof = token.kind == TokenKind::Eof;
            tokens.push(token);
            
            if is_eof {
                break;
            }
        }
        
        tokens
    }

    #[inline]
    fn current_char(&mut self) -> Option<char> {
        self.chars.peek().copied()
    }

    #[inline]
    fn advance(&mut self) -> Option<char> {
        let ch = self.chars.next();
        if let Some(c) = ch {
            // Correctly track byte position for Unicode characters
            self.current_pos += c.len_utf8();
            self.column += 1;
        }
        ch
    }

    #[inline]
    fn match_char(&mut self, expected: char) -> bool {
        if self.current_char() == Some(expected) {
            self.advance();
            true
        } else {
            false
        }
    }

    /// SIMD-optimized whitespace skipping for 2x performance boost
    fn skip_whitespace(&mut self) {
        #[cfg(target_arch = "x86_64")]
        {
            if is_x86_feature_detected!("sse2") {
                unsafe {
                    self.skip_whitespace_simd();
                    return;
                }
            }
        }

        // Fallback to scalar implementation
        self.skip_whitespace_scalar();
    }

    /// Scalar whitespace skipping (fallback)
    fn skip_whitespace_scalar(&mut self) {
        while let Some(ch) = self.current_char() {
            if ch.is_whitespace() && ch != '\n' {
                self.advance();
            } else {
                break;
            }
        }
    }

    /// SIMD-optimized whitespace skipping using SSE2
    #[cfg(target_arch = "x86_64")]
    unsafe fn skip_whitespace_simd(&mut self) {
        let remaining_bytes = &self.input_bytes[self.current_pos..];

        // Process 16 bytes at a time using SIMD
        let mut i = 0;
        while i + 16 <= remaining_bytes.len() {
            let chunk = _mm_loadu_si128(remaining_bytes.as_ptr().add(i) as *const __m128i);

            // Create masks for whitespace characters (space=32, tab=9)
            let spaces = _mm_set1_epi8(32);  // space
            let tabs = _mm_set1_epi8(9);     // tab
            let newlines = _mm_set1_epi8(10); // newline

            // Compare with whitespace characters
            let space_mask = _mm_cmpeq_epi8(chunk, spaces);
            let tab_mask = _mm_cmpeq_epi8(chunk, tabs);
            let newline_mask = _mm_cmpeq_epi8(chunk, newlines);

            // Combine masks (space OR tab, but NOT newline)
            let whitespace_mask = _mm_or_si128(space_mask, tab_mask);
            let not_newline_mask = _mm_xor_si128(newline_mask, _mm_set1_epi8(-1));
            let valid_whitespace = _mm_and_si128(whitespace_mask, not_newline_mask);

            // Get the mask as integer
            let mask = _mm_movemask_epi8(valid_whitespace) as u16;

            if mask == 0 {
                // No whitespace found in this chunk
                break;
            }

            // Count consecutive whitespace from the beginning
            let consecutive_whitespace = mask.trailing_zeros() as usize;
            if consecutive_whitespace == 0 {
                break;
            }

            // Advance by the number of consecutive whitespace characters
            for _ in 0..consecutive_whitespace {
                if self.current_char().map_or(false, |c| c.is_whitespace() && c != '\n') {
                    self.advance();
                } else {
                    return;
                }
            }

            i += consecutive_whitespace;

            if consecutive_whitespace < 16 {
                break;
            }
        }

        // Handle remaining bytes with scalar code
        self.skip_whitespace_scalar();
    }

    fn make_token(&self, kind: TokenKind, start_pos: usize, line: usize, column: usize) -> Token<'a> {
        let lexeme = &self.input[start_pos..self.current_pos];
        Token::new(kind, lexeme, line, column)
    }

    fn comment(&mut self) -> Token<'a> {
        let start_pos = self.current_pos;
        let start_line = self.line;
        let start_column = self.column;

        // Skip the '#'
        self.advance();

        // Read until end of line
        while let Some(ch) = self.current_char() {
            if ch == '\n' {
                break;
            }
            self.advance();
        }

        self.make_token(TokenKind::Comment, start_pos, start_line, start_column)
    }

    fn string_literal(&mut self) -> Token<'a> {
        let start_pos = self.current_pos;
        let start_line = self.line;
        let start_column = self.column;

        // Skip opening quote
        self.advance();

        while let Some(ch) = self.current_char() {
            if ch == '"' {
                self.advance(); // Skip closing quote
                break;
            }
            if ch == '\\' {
                self.advance(); // Skip escape character
                self.advance(); // Skip escaped character
            } else {
                self.advance();
            }
        }

        self.make_token(TokenKind::String, start_pos, start_line, start_column)
    }

    fn char_literal(&mut self) -> Token<'a> {
        let start_pos = self.current_pos;
        let start_line = self.line;
        let start_column = self.column;

        // Skip opening quote
        self.advance();

        if let Some(ch) = self.current_char() {
            if ch == '\\' {
                self.advance(); // Skip escape character
                self.advance(); // Skip escaped character
            } else {
                self.advance();
            }
        }

        // Skip closing quote
        if self.current_char() == Some('\'') {
            self.advance();
        }

        self.make_token(TokenKind::Char, start_pos, start_line, start_column)
    }

    fn number(&mut self) -> Token<'a> {
        let start_pos = self.current_pos;
        let start_line = self.line;
        let start_column = self.column;

        // Read integer part
        while let Some(ch) = self.current_char() {
            if ch.is_ascii_digit() {
                self.advance();
            } else {
                break;
            }
        }

        // Check for decimal point
        let mut is_float = false;
        if self.current_char() == Some('.') {
            // Look ahead to make sure it's not a method call like "42.toString()"
            let mut temp_chars = self.chars.clone();
            temp_chars.next(); // Skip the '.'
            if let Some(next_ch) = temp_chars.peek() {
                if next_ch.is_ascii_digit() {
                    is_float = true;
                    self.advance(); // Consume the '.'

                    // Read fractional part
                    while let Some(ch) = self.current_char() {
                        if ch.is_ascii_digit() {
                            self.advance();
                        } else {
                            break;
                        }
                    }
                }
            }
        }

        let lexeme = &self.input[start_pos..self.current_pos];

        // Use our ultra-fast type inference
        let token_kind = if is_float {
            let dolet_type = infer_type_from_token(lexeme);
            match dolet_type {
                crate::token::DoletType::Float => {
                    let value: f32 = lexeme.parse().unwrap_or(0.0);
                    TokenKind::Float(value)
                }
                crate::token::DoletType::Double => {
                    let value: f64 = lexeme.parse().unwrap_or(0.0);
                    TokenKind::Double(value)
                }
                _ => {
                    let value: f32 = lexeme.parse().unwrap_or(0.0);
                    TokenKind::Float(value)
                }
            }
        } else {
            let value: i64 = lexeme.parse().unwrap_or(0);
            TokenKind::Integer(value)
        };

        Token::new(token_kind, lexeme, start_line, start_column)
    }

    /// SIMD-optimized identifier scanning
    fn identifier_or_keyword(&mut self) -> Token<'a> {
        let start_pos = self.current_pos;
        let start_line = self.line;
        let start_column = self.column;

        // Use SIMD for identifier scanning if available
        #[cfg(target_arch = "x86_64")]
        {
            if is_x86_feature_detected!("sse2") {
                unsafe {
                    self.scan_identifier_simd();
                }
            } else {
                self.scan_identifier_scalar();
            }
        }

        #[cfg(not(target_arch = "x86_64"))]
        {
            self.scan_identifier_scalar();
        }

        let lexeme = &self.input[start_pos..self.current_pos];

        // Ultra-fast keyword lookup using HashMap
        let token_kind = if let Some(keyword_kind) = self.keywords.get(lexeme) {
            // Special handling for boolean literals
            match keyword_kind {
                TokenKind::True => TokenKind::Boolean(true),
                TokenKind::False => TokenKind::Boolean(false),
                _ => keyword_kind.clone(),
            }
        } else {
            TokenKind::Identifier
        };

        Token::new(token_kind, lexeme, start_line, start_column)
    }

    /// Scalar identifier scanning (fallback)
    fn scan_identifier_scalar(&mut self) {
        while let Some(ch) = self.current_char() {
            if ch.is_alphanumeric() || ch == '_' {
                self.advance();
            } else {
                break;
            }
        }
    }

    /// SIMD-optimized identifier scanning using SSE2
    #[cfg(target_arch = "x86_64")]
    unsafe fn scan_identifier_simd(&mut self) {
        let remaining_bytes = &self.input_bytes[self.current_pos..];

        // Process 16 bytes at a time
        let mut i = 0;
        while i + 16 <= remaining_bytes.len() {
            let chunk = _mm_loadu_si128(remaining_bytes.as_ptr().add(i) as *const __m128i);

            // Check for alphanumeric characters and underscore
            // ASCII: 'A'-'Z' (65-90), 'a'-'z' (97-122), '0'-'9' (48-57), '_' (95)
            let lower_a = _mm_set1_epi8(97);   // 'a'
            let upper_z = _mm_set1_epi8(122);  // 'z'
            let upper_a = _mm_set1_epi8(65);   // 'A'
            let upper_z_cap = _mm_set1_epi8(90); // 'Z'
            let digit_0 = _mm_set1_epi8(48);   // '0'
            let digit_9 = _mm_set1_epi8(57);   // '9'
            let underscore = _mm_set1_epi8(95); // '_'

            // Create masks for valid identifier characters
            let lowercase_mask = _mm_and_si128(
                _mm_cmpgt_epi8(chunk, _mm_sub_epi8(lower_a, _mm_set1_epi8(1))),
                _mm_cmplt_epi8(chunk, _mm_add_epi8(upper_z, _mm_set1_epi8(1)))
            );

            let uppercase_mask = _mm_and_si128(
                _mm_cmpgt_epi8(chunk, _mm_sub_epi8(upper_a, _mm_set1_epi8(1))),
                _mm_cmplt_epi8(chunk, _mm_add_epi8(upper_z_cap, _mm_set1_epi8(1)))
            );

            let digit_mask = _mm_and_si128(
                _mm_cmpgt_epi8(chunk, _mm_sub_epi8(digit_0, _mm_set1_epi8(1))),
                _mm_cmplt_epi8(chunk, _mm_add_epi8(digit_9, _mm_set1_epi8(1)))
            );

            let underscore_mask = _mm_cmpeq_epi8(chunk, underscore);

            // Combine all valid character masks
            let valid_mask = _mm_or_si128(
                _mm_or_si128(lowercase_mask, uppercase_mask),
                _mm_or_si128(digit_mask, underscore_mask)
            );

            // Get the mask as integer
            let mask = _mm_movemask_epi8(valid_mask) as u16;

            if mask == 0xFFFF {
                // All 16 characters are valid identifier characters
                for _ in 0..16 {
                    if self.current_char().map_or(false, |c| c.is_alphanumeric() || c == '_') {
                        self.advance();
                    } else {
                        return;
                    }
                }
                i += 16;
            } else {
                // Some characters are not valid, count consecutive valid ones
                let consecutive_valid = mask.trailing_ones() as usize;
                for _ in 0..consecutive_valid {
                    if self.current_char().map_or(false, |c| c.is_alphanumeric() || c == '_') {
                        self.advance();
                    } else {
                        return;
                    }
                }
                break;
            }
        }

        // Handle remaining bytes with scalar code
        self.scan_identifier_scalar();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_basic_tokenization() {
        let input = "set x = 42";
        let mut tokenizer = HyperdTokenizer::new(input);
        let tokens = tokenizer.tokenize();

        assert_eq!(tokens.len(), 5); // set, x, =, 42, EOF
        assert_eq!(tokens[0].kind, TokenKind::Set);
        assert_eq!(tokens[1].kind, TokenKind::Identifier);
        assert_eq!(tokens[2].kind, TokenKind::Assign);
        assert_eq!(tokens[3].kind, TokenKind::Integer(42));
        assert_eq!(tokens[4].kind, TokenKind::Eof);
    }

    #[test]
    fn test_string_and_numbers() {
        let input = r#"set name = "Hello" set pi = 3.14159"#;
        let mut tokenizer = HyperdTokenizer::new(input);
        let tokens = tokenizer.tokenize();

        // Find the string token
        let string_token = tokens.iter().find(|t| t.kind == TokenKind::String).unwrap();
        assert_eq!(string_token.lexeme, r#""Hello""#);

        // Find the float token
        let float_token = tokens.iter().find(|t| matches!(t.kind, TokenKind::Float(_))).unwrap();
        if let TokenKind::Float(value) = float_token.kind {
            assert!((value - 3.14159).abs() < 0.0001);
        }
    }

    #[test]
    fn test_keywords() {
        let input = "if true then say false end";
        let mut tokenizer = HyperdTokenizer::new(input);
        let tokens = tokenizer.tokenize();

        assert_eq!(tokens[0].kind, TokenKind::If);
        assert_eq!(tokens[1].kind, TokenKind::Boolean(true));
        assert_eq!(tokens[3].kind, TokenKind::Say);
        assert_eq!(tokens[4].kind, TokenKind::Boolean(false));
        assert_eq!(tokens[5].kind, TokenKind::End);
    }
}

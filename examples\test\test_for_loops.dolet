# Test For Loops - Check if they work
say "Testing For Loops..."

# Test 1: Basic for loop with range
say "Test 1: Basic for loop"
for i from 1 to 3:
    say "For loop iteration: " + i
end

# Test 2: Alternative for syntax
say "Test 2: Alternative for syntax"
for j = 0 to 2:
    say "Alternative for: " + j
end

# Test 3: For-in loop with array
say "Test 3: For-in loop with array"
set numbers = [1, 2, 3]
for num in numbers:
    say "Array item: " + num
end

say "For loops test complete!"

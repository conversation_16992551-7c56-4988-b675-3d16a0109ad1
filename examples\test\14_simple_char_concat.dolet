# Simple Character Concatenation Test
# Testing basic character concatenation

say "========================================="
say "Simple Character Concatenation Test"
say "========================================="

# Basic character literals
set char_a = 'A'
set char_b = 'B'

say "Basic Character Concatenation:"
say "char_a = " + char_a
say "char_b = " + char_b
say "Combined: " + char_a + char_b

say "========================================="
say "Simple Character Concatenation Test Complete"
say "========================================="

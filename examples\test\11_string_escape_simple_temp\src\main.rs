use std::io::{self, Write};


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Simple String Escape Test");
    println!("{}", "=========================================");
    let mut quoted_string = "She said Hello World";
    println!("{}", format!("{}{}" , "Basic string: ", quoted_string));
    let mut escaped_quote = "She said \"Hello World\"";
    println!("{}", format!("{}{}" , "Escaped quotes: ", escaped_quote));
    println!("{}", "=========================================");
    println!("{}", "Simple String Escape Test Complete");
    println!("{}", "=========================================");
}

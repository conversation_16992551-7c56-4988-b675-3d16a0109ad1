use std::io::{self, Write};

fn calculate_area(length: i64, width: i64) -> i64 {
    return (length * width);
}

fn greet_person(name: &str) -> String {
    return format!("{}{}" , format!("{}{}" , "Hello, ", name), "!");
}

fn is_even(num: i64) -> bool {
    return ((num % 2) == 0);
}

fn factorial(n: i64) -> i64 {
    if (n <= 1) {
        return 1;
    } else {
        return (n * factorial((n - 1)));
    }
    0
}


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Comprehensive Language Test");
    println!("{}", "=========================================");
    println!("{}", "=== Data Types and Variables ===");
    let mut int_var = 42;
    let mut float_var = 3.14159f32;
    let mut string_var = "Hello World";
    let mut bool_var = true;
    let mut null_var = "null";
    println!("{}", format!("{}{}" , "Integer: ", int_var));
    println!("{}", format!("{}{}" , "Float: ", float_var));
    println!("{}", format!("{}{}" , "String: ", string_var));
    println!("{}", format!("{}{}" , "Boolean: ", bool_var));
    println!("{}", format!("{}{}" , "Null: ", null_var));
    println!("{}", "");
    println!("{}", "=== Arrays ===");
    let mut numbers = vec![1, 2, 3, 4, 5];
    let mut words = vec!["apple", "banana", "cherry"];
    let mut flags = vec![true, false, true, false];
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Numbers array: ", numbers[0 as usize]), ", "), numbers[1 as usize]), ", "), numbers[2 as usize]));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Words array: ", words[0 as usize]), ", "), words[1 as usize]), ", "), words[2 as usize]));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Flags array: ", flags[0 as usize]), ", "), flags[1 as usize]), ", "), flags[2 as usize]));
    println!("{}", "");
    println!("{}", "=== Arithmetic Operations ===");
    let mut a = 15;
    let mut b = 4;
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "a = ", a), ", b = "), b));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Addition: ", a), " + "), b), " = "), (a + b)));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Subtraction: ", a), " - "), b), " = "), (a - b)));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Multiplication: ", a), " * "), b), " = "), (a * b)));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Division: ", a), " / "), b), " = "), (a / b)));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Modulo: ", a), " % "), b), " = "), (a % b)));
    println!("{}", "");
    println!("{}", "=== Boolean Operations ===");
    let mut x = true;
    let mut y = false;
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "x = ", x), ", y = "), y));
    println!("{}", format!("{}{}" , "x AND y: ", (x && y)));
    println!("{}", format!("{}{}" , "x OR y: ", (x || y)));
    println!("{}", format!("{}{}" , "NOT x: ", (!x)));
    println!("{}", format!("{}{}" , "NOT y: ", (!y)));
    println!("{}", "");
    println!("{}", "=== Comparison Operations ===");
    let mut p = 10;
    let mut q = 20;
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "p = ", p), ", q = "), q));
    println!("{}", format!("{}{}" , "p == q: ", (p == q)));
    println!("{}", format!("{}{}" , "p != q: ", (p != q)));
    println!("{}", format!("{}{}" , "p < q: ", (p < q)));
    println!("{}", format!("{}{}" , "p > q: ", (p > q)));
    println!("{}", format!("{}{}" , "p <= q: ", (p <= q)));
    println!("{}", format!("{}{}" , "p >= q: ", (p >= q)));
    println!("{}", "");
    println!("{}", "=== Control Flow - If/Else ===");
    let mut score = 85;
    println!("{}", format!("{}{}" , "Score: ", score));
    if (score >= 90) {
        println!("{}", "Grade: A (Excellent)");
    } else {
        if (score >= 80) {
            println!("{}", "Grade: B (Good)");
        } else {
            // Unsupported nested statement
        }
    }
    println!("{}", "");
    println!("{}", "=== Control Flow - Loops ===");
    println!("{}", "While loop (counting to 5):");
    let mut counter = 1;
    while (counter <= 5) {
        println!("{}", format!("{}{}" , "Count: ", counter));
        counter = (counter + 1);
    }
    println!("{}", "For loop (1 to 5):");
    for i in 1..=5 {
        println!("{}", format!("{}{}" , "For loop iteration: ", i));
    }
    println!("{}", "");
    println!("{}", "=== Functions ===");
    let mut area = calculate_area(5, 3);
    let mut greeting = greet_person("Alice".to_string());
    let mut even_check = is_even(8);
    let mut fact_5 = factorial(5);
    println!("{}", format!("{}{}" , "Area of 5x3 rectangle: ", area));
    println!("{}", format!("{}{}" , "Greeting: ", greeting));
    println!("{}", format!("{}{}" , "Is 8 even? ", even_check));
    println!("{}", format!("{}{}" , "Factorial of 5: ", fact_5));
    println!("{}", "");
    println!("{}", "=== Array Processing ===");
    let mut test_numbers = vec![2, 4, 6, 8, 10];
    println!("{}", "Testing even numbers:");
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "is_even(", test_numbers[0 as usize]), ") = "), is_even(test_numbers[0 as usize])));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "is_even(", test_numbers[1 as usize]), ") = "), is_even(test_numbers[1 as usize])));
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "is_even(", test_numbers[2 as usize]), ") = "), is_even(test_numbers[2 as usize])));
    println!("{}", "");
    println!("{}", "=== Complex Expressions ===");
    let mut complex1 = (((10 + 5) * 2) - 3);
    let mut complex2 = (numbers[0 as usize] + (numbers[4 as usize] * 2));
    let mut complex3 = calculate_area(numbers[1 as usize], numbers[2 as usize]);
    println!("{}", format!("{}{}" , "Complex arithmetic: (10 + 5) * 2 - 3 = ", complex1));
    println!("{}", format!("{}{}" , "Array arithmetic: numbers[0] + numbers[4] * 2 = ", complex2));
    println!("{}", format!("{}{}" , "Function with array: calculate_area(numbers[1], numbers[2]) = ", complex3));
    println!("{}", "");
    println!("{}", "=== String Operations ===");
    let mut first_name = "John";
    let mut last_name = "Doe";
    let mut age = 30;
    let mut full_info = format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , first_name, " "), last_name), " is "), age), " years old");
    println!("{}", format!("{}{}" , "Full info: ", full_info));
    let mut message = greet_person(format!("{}{}" , format!("{}{}" , first_name, " "), last_name));
    println!("{}", format!("{}{}" , "Message: ", message));
    println!("{}", "");
    println!("{}", "=== Mixed Data Type Operations ===");
    let mut mixed_result = format!("{}{}" , format!("{}{}" , format!("{}{}" , "Result: ", (numbers[0 as usize] + numbers[1 as usize])), " is "), is_even((numbers[0 as usize] + numbers[1 as usize])));
    println!("{}", mixed_result);
    let mut status = format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Processing ", words[0 as usize]), " with value "), numbers[0 as usize]), " - "), flags[0 as usize]);
    println!("{}", status);
    println!("{}", "");
    println!("{}", "=== Performance Demonstration ===");
    let mut start_num = 1;
    let mut end_num = 10;
    let mut sum_total = 0;
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Calculating sum from ", start_num), " to "), end_num), ":"));
    let mut current = start_num;
    while (current <= end_num) {
        sum_total = (sum_total + current);
        current = (current + 1);
    }
    println!("{}", format!("{}{}" , "Sum: ", sum_total));
    println!("{}", format!("{}{}" , "Average: ", (sum_total / end_num)));
    println!("{}", "=========================================");
    println!("{}", "Comprehensive Test Complete!");
    println!("{}", "All major language features tested successfully.");
    println!("{}", "=========================================");
}

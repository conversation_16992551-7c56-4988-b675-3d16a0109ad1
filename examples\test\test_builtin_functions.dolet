# Test Built-in Functions - Check if they work
say "Testing Built-in Functions..."

# Test 1: Math functions
say "Test 1: Math functions"
set sqrt_result = sqrt(16)
say "sqrt(16) = " + sqrt_result

set power_result = power(2, 3)
say "power(2, 3) = " + power_result

set abs_result = abs(-5)
say "abs(-5) = " + abs_result

# Test 2: String functions
say "Test 2: String functions"
set str_length = length("hello")
say "length('hello') = " + str_length

set uppercase = upper("hello")
say "upper('hello') = " + uppercase

set lowercase = lower("WORLD")
say "lower('WORLD') = " + lowercase

# Test 3: Array functions
say "Test 3: Array functions"
set numbers = [1, 2, 3, 4, 5]
set array_length = length(numbers)
say "length([1,2,3,4,5]) = " + array_length

set array_sum = sum(numbers)
say "sum([1,2,3,4,5]) = " + array_sum

# Test 4: Type conversion functions
say "Test 4: Type conversion"
set str_to_int = int("123")
say "int('123') = " + str_to_int

set int_to_str = string(456)
say "string(456) = " + int_to_str

set str_to_float = float("3.14")
say "float('3.14') = " + str_to_float

say "Built-in functions test complete!"

# Simple Data Types Test
# Testing basic data types without problematic features

say "========================================="
say "Simple Data Types Test"
say "========================================="

# Integer types
set small_int = 42
set large_int = 999999
set negative_int = -123
set zero_int = 0

say "Integer Types:"
say "small_int = " + small_int
say "large_int = " + large_int
say "negative_int = " + negative_int
say "zero_int = " + zero_int
say ""

# Float types
set simple_float = 3.14
set precise_float = 2.71828
set negative_float = -3.14159
set zero_float = 0.0

say "Float Types:"
say "simple_float = " + simple_float
say "precise_float = " + precise_float
say "negative_float = " + negative_float
say "zero_float = " + zero_float
say ""

# Double types
set pi_double = 3.141592653589793
set e_double = 2.718281828459045
set negative_double = -2.718281828459045

say "Double Types:"
say "pi_double = " + pi_double
say "e_double = " + e_double
say "negative_double = " + negative_double
say ""

# Boolean types
set true_bool = true
set false_bool = false
set bool_from_comparison = 5 > 3
set bool_from_equality = 10 == 10

say "Boolean Types:"
say "true_bool = " + true_bool
say "false_bool = " + false_bool
say "bool_from_comparison = " + bool_from_comparison
say "bool_from_equality = " + bool_from_equality
say ""

# String types
set simple_string = "Hello World"
set empty_string = ""
set string_with_numbers = "Test123"
set string_with_spaces = "   Spaces   "

say "String Types:"
say "simple_string = " + simple_string
say "empty_string = '" + empty_string + "'"
say "string_with_numbers = " + string_with_numbers
say "string_with_spaces = '" + string_with_spaces + "'"
say ""

# Null types
set null_value = null
set nullable_int = null

say "Null Types:"
say "null_value = " + null_value
say "nullable_int = " + nullable_int
say ""

# Type mixing
set mixed_concat = "Number: " + 42 + ", Float: " + 3.14 + ", Bool: " + true
set complex_expression = "Result: " + (10 + 5) + " is " + (10 > 5)

say "Type Mixing:"
say "mixed_concat = " + mixed_concat
say "complex_expression = " + complex_expression

say "========================================="
say "Simple Data Types Test Complete"
say "========================================="

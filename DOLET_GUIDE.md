# 📚 Dolet Programming Language - Complete Guide

A comprehensive guide to the Dolet programming language with all working features, syntax, and examples.

## 🚀 **Quick Start**

### Installation & First Program
```bash
# Clone and build
git clone https://github.com/your-username/dolet-compiler
cd dolet-compiler
cargo build --release

# Create your first program
echo 'say "Hello, <PERSON><PERSON>!"' > hello.dolet

# Compile and run
cargo run --release --bin dolet -- hello.dolet --time
./hello.exe
```

## 📖 **Language Syntax**

### ✅ **Variables & Constants**
```dolet
# Variable declarations with type inference
set name = "Alice"           # string
set age = 25                 # int
set height = 5.8             # float
set weight = 70.*********    # double (6+ decimal places)
set active = true            # bool
set data = null              # null

# Explicit type declarations
set count: int = 100
set price: float = 19.99
set precision: double = 3.141592653589793

# Constants (immutable)
const PI = 3.14159
const MAX_USERS = 1000
const APP_NAME = "Dolet App"
```

### ✅ **Data Types**
| Type | Description | Examples |
|------|-------------|----------|
| `int` | 64-bit signed integer | `42`, `-100`, `1000000` |
| `float` | 32-bit floating point | `3.14`, `2.5`, `-1.5` |
| `double` | 64-bit floating point | `3.*********`, `2.*********` |
| `string` | UTF-8 text | `"Hello"`, `"World!"` |
| `char` | Single character | `'A'`, `'5'`, `'@'` |
| `bool` | Boolean value | `true`, `false` |
| `null` | Null value | `null` |
| `array` | Collection | `[1, 2, 3]`, `["a", "b"]` |

### ✅ **Operators**

#### Arithmetic Operators
```dolet
set a = 10
set b = 3

set sum = a + b          # 13
set difference = a - b   # 7
set product = a * b      # 30
set quotient = a / b     # 3
set remainder = a % b    # 1
```

#### Comparison Operators
```dolet
set x = 5
set y = 10

set equal = (x == y)         # false
set not_equal = (x != y)     # true
set less_than = (x < y)      # true
set greater_than = (x > y)   # false
set less_equal = (x <= y)    # true
set greater_equal = (x >= y) # false
```

#### Logical Operators
```dolet
set p = true
set q = false

set and_result = p and q     # false
set or_result = p or q       # true
set not_result = not p       # false

# Complex logical expressions
set complex = (age >= 18) and (score > 80) or (vip == true)
```

#### Assignment Operators
```dolet
set counter = 10
set counter += 5    # counter = 15
set counter -= 3    # counter = 12
set counter *= 2    # counter = 24
set counter /= 4    # counter = 6
```

### ✅ **Control Flow**

#### If Statements
```dolet
# Simple if-else
if age >= 18:
    say "You are an adult"
else:
    say "You are a minor"
end

# Multiple conditions
if score >= 90:
    say "Grade: A"
else if score >= 80:
    say "Grade: B"
else if score >= 70:
    say "Grade: C"
else:
    say "Grade: F"
end

# Nested conditions
if logged_in:
    if is_admin:
        say "Welcome, Admin!"
    else:
        say "Welcome, User!"
    end
else:
    say "Please log in"
end
```

#### While Loops
```dolet
# Basic while loop
set count = 0
while count < 5:
    say "Count: " + count
    set count = count + 1
end

# Alternative syntax
set i = 0
while i < 3 do
    say "Alternative syntax: " + i
    set i += 1
end
```

#### For Loops (Range-based)
```dolet
# Basic for loop
for i from 1 to 5:
    say "For loop iteration: " + i
end

# Alternative syntax
for j = 0 to 4:
    say "Alternative for: " + j
end

# Nested for loops
for x from 1 to 3:
    for y from 1 to 3:
        say "(" + x + ", " + y + ")"
    end
end
```

#### For-In Loops (Array Iteration)
```dolet
# Iterate over array elements
set numbers = [1, 2, 3, 4, 5]
for num in numbers:
    say "Array item: " + num
end

# Iterate over string array
set names = ["Alice", "Bob", "Charlie"]
for name in names:
    say "Hello, " + name + "!"
end
```

### ✅ **Arrays**

#### Array Creation & Access
```dolet
# Create arrays
set numbers = [1, 2, 3, 4, 5]
set names = ["Alice", "Bob", "Charlie"]
set mixed = [1, "hello", true, 3.14]

# Array indexing (0-based)
set first = numbers[0]      # 1
set second = numbers[1]     # 2
set last = numbers[4]       # 5

# Array modification
set numbers[0] = 100        # [100, 2, 3, 4, 5]
set names[1] = "Robert"     # ["Alice", "Robert", "Charlie"]
```

#### Array Operations
```dolet
set data = [10, 20, 30, 40, 50]

# Get array length
set size = length(data)     # 5

# Calculate sum
set total = sum(data)       # 150

# Iterate and process
for item in data:
    if item > 25:
        say "Large item: " + item
    end
end
```

### ✅ **Functions**

#### Function Declaration
```dolet
# Simple function
fun greet(name):
    say "Hello, " + name + "!"
end

# Function with return value
fun add(a, b):
    return a + b
end

# Function with type annotations
fun multiply(x: int, y: int) -> int:
    return x * y
end

# Function with multiple parameters
fun calculate_area(length: float, width: float) -> float:
    return length * width
end
```

#### Function Calls
```dolet
# Call functions
greet("Alice")                    # Output: Hello, Alice!
set result = add(10, 20)         # result = 30
set product = multiply(5, 6)     # product = 30
set area = calculate_area(4.5, 3.2)  # area = 14.4

# Use function results in expressions
set total = add(multiply(3, 4), add(5, 6))  # total = 23
say "Total: " + total
```

### ✅ **Built-in Functions**

#### Math Functions
```dolet
# Basic math operations
set sqrt_result = sqrt(16)        # 4.0
set power_result = power(2, 3)    # 8.0
set abs_result = abs(-5)          # 5.0

# Use in calculations
set hypotenuse = sqrt(power(3, 2) + power(4, 2))  # 5.0
```

#### String Functions
```dolet
set text = "Hello World"

# String operations
set length_result = length(text)    # 11
set upper_result = upper(text)      # "HELLO WORLD"
set lower_result = lower(text)      # "hello world"

# String concatenation
set message = "Length of '" + text + "' is " + length(text)
```

#### Array Functions
```dolet
set numbers = [1, 2, 3, 4, 5]

# Array operations
set array_length = length(numbers)  # 5
set array_sum = sum(numbers)        # 15

# Process arrays
say "Array has " + length(numbers) + " elements"
say "Sum is " + sum(numbers)
```

#### Type Conversion Functions
```dolet
# Convert between types
set str_to_int = int("123")       # 123
set int_to_str = string(456)      # "456"
set str_to_float = float("3.14")  # 3.14

# Use in expressions
set user_age = int(user_input)
set display_text = "Age: " + string(user_age)
```

### ✅ **User Input & Output**

#### Output
```dolet
# Simple output
say "Hello, World!"

# Output with variables
set name = "Alice"
set age = 25
say "Name: " + name + ", Age: " + age

# Output with expressions
say "The answer is: " + (20 + 22)
```

#### User Input
```dolet
# Prompt user for input
ask "What is your name? "
set user_name = input
say "Hello, " + user_name + "!"

# Multiple inputs
ask "Enter your age: "
set user_age = int(input)

ask "Enter your height: "
set user_height = float(input)

# Use input in calculations
if user_age >= 18:
    say "You are an adult"
else:
    say "You are " + (18 - user_age) + " years away from being an adult"
end
```

### ✅ **File Operations**

#### File Writing & Reading
```dolet
# Write to file
write "output.txt", "Hello, World!"
write "data.txt", "Line 1\nLine 2\nLine 3"

# Read from file
set file_content = read "output.txt"
say "File contains: " + file_content

# Append to file
append "output.txt", "\nNew line added"
set updated_content = read "output.txt"
say "Updated content: " + updated_content
```

#### File Processing Example
```dolet
# Create a log file
set log_message = "Application started at " + string(123456)
write "app.log", log_message

# Read and process
set log_content = read "app.log"
say "Log: " + log_content

# Add more entries
append "app.log", "\nUser logged in"
append "app.log", "\nData processed"

# Read final log
set final_log = read "app.log"
say "Complete log:\n" + final_log
```

## 🎯 **Complete Examples**

### Example 1: Interactive Calculator
```dolet
say "=== Simple Calculator ==="

ask "Enter first number: "
set num1 = float(input)

ask "Enter second number: "
set num2 = float(input)

ask "Enter operation (+, -, *, /): "
set operation = input

if operation == "+":
    set result = num1 + num2
    say "Result: " + num1 + " + " + num2 + " = " + result
else if operation == "-":
    set result = num1 - num2
    say "Result: " + num1 + " - " + num2 + " = " + result
else if operation == "*":
    set result = num1 * num2
    say "Result: " + num1 + " * " + num2 + " = " + result
else if operation == "/":
    if num2 != 0:
        set result = num1 / num2
        say "Result: " + num1 + " / " + num2 + " = " + result
    else:
        say "Error: Division by zero!"
    end
else:
    say "Error: Invalid operation!"
end
```

### Example 2: Array Processing
```dolet
say "=== Array Processing Demo ==="

# Create and populate array
set numbers = [10, 25, 7, 42, 18, 33, 9]
say "Original array: " + string(numbers)

# Find statistics
set total = sum(numbers)
set count = length(numbers)
set average = total / count

say "Total: " + total
say "Count: " + count
say "Average: " + average

# Find min and max
set min_val = numbers[0]
set max_val = numbers[0]

for num in numbers:
    if num < min_val:
        set min_val = num
    end
    if num > max_val:
        set max_val = num
    end
end

say "Minimum: " + min_val
say "Maximum: " + max_val

# Filter and display
say "Numbers greater than average (" + average + "):"
for num in numbers:
    if num > average:
        say "  " + num
    end
end
```

### Example 3: File-Based Data Processing
```dolet
say "=== File Processing Demo ==="

# Create sample data file
set data = "Alice,25,Engineer\nBob,30,Designer\nCharlie,28,Developer"
write "employees.csv", data
say "Created employees.csv file"

# Read and process data
set file_content = read "employees.csv"
say "File content:"
say file_content

# Create summary report
set report = "Employee Summary Report\n"
set report = report + "========================\n"
set report = report + "Total employees: 3\n"
set report = report + "Data processed successfully\n"

write "report.txt", report
say "Created report.txt"

# Read and display report
set final_report = read "report.txt"
say "\nGenerated Report:"
say final_report
```

### Example 4: Mathematical Functions Demo
```dolet
say "=== Mathematical Functions Demo ==="

# Test various math functions
set numbers = [16, 25, 36, 49, 64]
say "Testing square roots:"

for num in numbers:
    set sqrt_result = sqrt(num)
    say "sqrt(" + num + ") = " + sqrt_result
end

# Power calculations
say "\nTesting power function:"
for i from 1 to 5:
    set power_result = power(2, i)
    say "2^" + i + " = " + power_result
end

# Absolute values
set test_values = [-10, -5, 0, 5, 10]
say "\nTesting absolute values:"

for val in test_values:
    set abs_result = abs(val)
    say "abs(" + val + ") = " + abs_result
end
```

## 🔧 **Best Practices**

### Code Organization
```dolet
# Use meaningful variable names
set user_age = 25              # Good
set a = 25                     # Avoid

# Group related functionality
fun calculate_circle_area(radius: float) -> float:
    const PI = 3.14159
    return PI * radius * radius
end

fun calculate_circle_circumference(radius: float) -> float:
    const PI = 3.14159
    return 2 * PI * radius
end
```

### Error Handling
```dolet
# Check for valid input
ask "Enter a number: "
set user_input = input

if user_input != "":
    set number = float(user_input)
    say "You entered: " + number
else:
    say "No input provided"
end

# Validate array access
set data = [1, 2, 3]
set index = 1

if index >= 0 and index < length(data):
    set value = data[index]
    say "Value at index " + index + ": " + value
else:
    say "Invalid index: " + index
end
```

### Performance Tips
```dolet
# Prefer constants for unchanging values
const MAX_ITERATIONS = 1000

# Use appropriate data types
set counter: int = 0           # For whole numbers
set percentage: float = 85.5   # For decimals

# Minimize string concatenation in loops
set result = ""
set parts = ["Hello", " ", "World", "!"]

for part in parts:
    set result = result + part
end
```

## 🚀 **Compilation & Execution**

### Command Line Usage
```bash
# Basic compilation
cargo run --release --bin dolet -- program.dolet

# With timing information
cargo run --release --bin dolet -- program.dolet --time

# Debug output (for development)
cargo run --release --bin dolet -- program.dolet --tokens --ast

# Run the generated executable
./program.exe        # Windows
./program           # Linux/macOS
```

### Performance Metrics
- **File Reading**: ~120µs
- **Tokenization**: ~20µs
- **Parsing**: ~50µs
- **Semantic Analysis**: ~10µs
- **Code Generation**: ~1.7s
- **Total Compilation**: ~1.8s

### Optimization Flags
```bash
# Maximum optimization
cargo run --release --bin dolet -- program.dolet

# Development mode (faster compilation)
cargo run --bin dolet -- program.dolet
```

## 📚 **Learning Path**

### Beginner (Start Here)
1. **Variables & Types** - Learn basic data types and declarations
2. **Output** - Use `say` to display results
3. **Input** - Use `ask` and `input` for user interaction
4. **Arithmetic** - Basic math operations
5. **Control Flow** - if/else statements

### Intermediate
1. **Loops** - while, for, and for-in loops
2. **Arrays** - Creation, indexing, and iteration
3. **Functions** - Create reusable code blocks
4. **Built-in Functions** - Math, string, and array operations
5. **File Operations** - Read, write, and append files

### Advanced
1. **Complex Expressions** - Nested operations and function calls
2. **Data Processing** - Array manipulation and analysis
3. **Interactive Programs** - User input with validation
4. **File Processing** - Data import/export and reporting
5. **Algorithm Implementation** - Sorting, searching, calculations

## 🎯 **Next Steps**

After mastering the current features, explore:
- **Object-Oriented Programming** (coming soon)
- **Module System** (planned)
- **Error Handling** (try/catch - planned)
- **Advanced Data Structures** (planned)
- **Concurrency** (planned)

---

**Happy coding with Dolet!** 🚀

*For more examples, check the `examples/` directory in the project repository.*

# Simple String Operations Test
# Testing basic string operations without complex features

say "========================================="
say "Simple String Test"
say "========================================="

# Basic string variables
set str1 = "Hello"
set str2 = "World"
set str3 = ""
set str4 = " "
set str5 = "123"

say "Basic String Variables:"
say "str1 = " + str1
say "str2 = " + str2
say "str3 = '" + str3 + "'"
say "str4 = '" + str4 + "'"
say "str5 = " + str5
say ""

# Simple string concatenation with say
say "String Concatenation with say:"
say str1 + " " + str2
say str1 + str2
say str1 + str3 + str2
say str3 + str1 + str3
say ""

# String with numbers
set num1 = 42
set num2 = 3
set bool1 = true

say "String with Numbers and Booleans:"
say "Number: " + num1
say "Another number: " + num2
say "Boolean: " + bool1
say "Sum: " + (num1 + num2)
say ""

# Multiple concatenations
say "Multiple Concatenations:"
say "Values: " + num1 + ", " + num2 + ", " + bool1
say str1 + " " + str2 + " " + num1
say "Result: " + (num1 + num2) + " is " + (num1 > num2)
say ""

# Empty and space strings
say "Empty and Space Strings:"
say "Before" + str3 + "After"
say "Before" + str4 + "After"
say "[" + str3 + "]"
say "[" + str4 + "]"
say ""

# Long strings
set long_str = "This is a longer string for testing purposes"
say "Long String:"
say long_str
say "Length test: " + long_str + " - complete"
say ""

# String with special characters (simple ones)
set special1 = "Test!@#$%"
set special2 = "Numbers123"
set special3 = "Spaces   Here"

say "Special Characters:"
say special1
say special2
say special3
say ""

# Repeated concatenation
set repeat_base = "Test"
say "Repeated Concatenation:"
say repeat_base
say repeat_base + repeat_base
say repeat_base + repeat_base + repeat_base
say ""

# Mixed with different data types
say "Mixed Data Types:"
say "Integer: " + 100
say "Float: " + 3.14
say "Boolean: " + false
say "Null: " + null

say "========================================="
say "Simple String Test Complete"
say "========================================="

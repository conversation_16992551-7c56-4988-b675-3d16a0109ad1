# Comprehensive String Operations Test
# Testing string concatenation, manipulation, and edge cases

say "========================================="
say "String Operations Test"
say "========================================="

# Basic string variables
set str1 = "Hello"
set str2 = "World"
set str3 = ""
set str4 = " "
set str5 = "123"
set str6 = "Test"

say "Basic String Variables:"
say "str1 = '" + str1 + "'"
say "str2 = '" + str2 + "'"
say "str3 = '" + str3 + "'"
say "str4 = '" + str4 + "'"
say "str5 = '" + str5 + "'"
say "str6 = '" + str6 + "'"
say ""

# String concatenation
set concat1 = str1 + str2
set concat2 = str1 + " " + str2
set concat3 = str1 + str3 + str2
set concat4 = str3 + str1 + str3
set concat5 = str1 + str4 + str2

say "String Concatenation:"
say "'" + str1 + "' + '" + str2 + "' = '" + concat1 + "'"
say "'" + str1 + "' + ' ' + '" + str2 + "' = '" + concat2 + "'"
say "'" + str1 + "' + '" + str3 + "' + '" + str2 + "' = '" + concat3 + "'"
say "'" + str3 + "' + '" + str1 + "' + '" + str3 + "' = '" + concat4 + "'"
say "'" + str1 + "' + '" + str4 + "' + '" + str2 + "' = '" + concat5 + "'"
say ""

# String with numbers
set num1 = 42
set num2 = 3.14
set bool1 = true
set bool2 = false

set str_num1 = "Number: " + num1
set str_num2 = "Pi is approximately " + num2
set str_bool1 = "Is active: " + bool1
set str_bool2 = "Is complete: " + bool2

say "String with Numbers and Booleans:"
say str_num1
say str_num2
say str_bool1
say str_bool2
say ""

# Complex string concatenation
set complex1 = "Result: " + num1 + " + " + num2 + " = " + (num1 + num2)
set complex2 = str1 + " " + str2 + "! Today is " + bool1 + " day."
set complex3 = "Values: [" + num1 + ", " + num2 + ", " + bool1 + "]"

say "Complex String Operations:"
say complex1
say complex2
say complex3
say ""

# String with special characters
set special1 = "Quote: \"Hello World\""
set special2 = "Path: C:\\Users\\<USER>\nSecond line"
set special5 = "Tab test\tTabbed text"

say "Special Characters:"
say special1
say special2
say special3
say special4
say special5
say ""

# Empty and whitespace strings
set empty = ""
set spaces = "   "
set tabs = "\t\t"
set mixed_whitespace = " \t \t "

set empty_concat = empty + "Added"
set spaces_concat = "Before" + spaces + "After"
set whitespace_test = "[" + mixed_whitespace + "]"

say "Empty and Whitespace Strings:"
say "Empty + 'Added' = '" + empty_concat + "'"
say "'Before' + spaces + 'After' = '" + spaces_concat + "'"
say "Mixed whitespace = '" + whitespace_test + "'"
say ""

# Long strings
set long_str1 = "This is a very long string that contains many words and should test the string handling capabilities of the language."
set long_str2 = "Another long string for concatenation testing purposes with different content and structure."
set long_concat = long_str1 + " " + long_str2

say "Long Strings:"
say "Long string 1: " + long_str1
say "Long string 2: " + long_str2
say "Concatenated: " + long_concat
say ""

# Repeated concatenation
set repeat_base = "Test"
set repeat1 = repeat_base + repeat_base
set repeat2 = repeat1 + repeat1
set repeat3 = repeat2 + repeat2

say "Repeated Concatenation:"
say "Base: '" + repeat_base + "'"
say "x2: '" + repeat1 + "'"
say "x4: '" + repeat2 + "'"
say "x8: '" + repeat3 + "'"
say ""

# String with various data types
set mixed_string = "Mixed: " + 42 + ", " + 3.14159 + ", " + true + ", " + null + ", '" + 'A' + "'"

say "Mixed Data Types in String:"
say mixed_string

say "========================================="
say "String Operations Test Complete"
say "========================================="

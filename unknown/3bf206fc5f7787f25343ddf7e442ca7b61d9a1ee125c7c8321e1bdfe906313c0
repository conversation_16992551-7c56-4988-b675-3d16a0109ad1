# Final Comprehensive Test - All Working Features
# Optimized to work within current parser limitations

# ===== VARIABLE DECLARATIONS & TYPE INFERENCE =====
set program_name = "Dolet Final Test"
set version = 1.0
set test_count = 15
set passed_tests = 0

# Different numeric types
set small_int = 42
set large_int = 999999
set negative_int = -123
set simple_float = 3.14
set precise_double = 3.141592653589793

# Boolean and null
set is_testing = true
set is_complete = false
set optional_data = null

# ===== EXPLICIT TYPE DECLARATIONS =====
set width: int = 1920
set height: int = 1080
set aspect_ratio: float = 1.777
set precision: double = 0.000000001
set title: string = "Test Window"
set debug_mode: bool = true

# ===== CONSTANTS =====
const PI = 3.141592653589793
const E = 2.718281828459045
const GOLDEN_RATIO = 1.618033988749895

# ===== MATHEMATICAL FUNCTIONS =====

# Simple factorial function
fun factorial(n: int) -> int:
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)
    end
end

# Power function
fun power(base: float, exponent: int) -> float:
    if exponent == 0:
        return 1.0
    else:
        if exponent > 0:
            return base * power(base, exponent - 1)
        else:
            return 1.0 / power(base, -exponent)
        end
    end
end

# Simple arithmetic functions
fun add(a: int, b: int) -> int:
    return a + b
end

fun multiply(a: float, b: float) -> float:
    return a * b
end

# ===== ARRAYS =====
set numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
set primes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29]
set test_scores = [95.5, 87.2, 92.8, 78.9, 88.1]
set languages = ["Rust", "C++", "Python", "JavaScript", "Go"]

# ===== COMPLEX CALCULATIONS =====

# Mathematical calculations
set circle_area = PI * power(5.0, 2)
set sphere_volume = (4.0 / 3.0) * PI * power(3.0, 3)

# Function calls
set fact_5 = factorial(5)
set fact_7 = factorial(7)
set power_result = power(2.0, 10)

# Complex expressions
set sum_result = add(10, 20) + add(30, 40)
set product_result = multiply(3.14, 2.0) * multiply(1.5, 4.0)
set complex_calc = (fact_5 + sum_result) * circle_area / PI

# ===== CONDITIONAL LOGIC =====

# Test version check
if version >= 1.0:
    say "Version check: PASSED"
    set passed_tests = passed_tests + 1
else:
    say "Version check: FAILED"
end

# Test factorial calculation
if fact_5 == 120:
    say "Factorial test: PASSED (5! = " + fact_5 + ")"
    set passed_tests = passed_tests + 1
else:
    say "Factorial test: FAILED (5! = " + fact_5 + ")"
end

# Test power calculation
if power_result == 1024.0:
    say "Power test: PASSED (2^10 = " + power_result + ")"
    set passed_tests = passed_tests + 1
else:
    say "Power test: FAILED (2^10 = " + power_result + ")"
end

# Test circle area
if circle_area > 75.0:
    say "Circle area test: PASSED (" + circle_area + ")"
    set passed_tests = passed_tests + 1
else:
    say "Circle area test: FAILED (" + circle_area + ")"
end

# Test boolean logic
if is_testing == true:
    if debug_mode == true:
        say "Boolean logic test: PASSED"
        set passed_tests = passed_tests + 1
    else:
        say "Boolean logic test: FAILED (debug_mode)"
    end
else:
    say "Boolean logic test: FAILED (is_testing)"
end

# Test type inference
if small_int == 42:
    say "Type inference test: PASSED"
    set passed_tests = passed_tests + 1
else:
    say "Type inference test: FAILED"
end

# Test null handling
if optional_data == null:
    say "Null handling test: PASSED"
    set passed_tests = passed_tests + 1
else:
    say "Null handling test: FAILED"
end

# Test string operations
if title == "Test Window":
    say "String test: PASSED"
    set passed_tests = passed_tests + 1
else:
    say "String test: FAILED"
end

# Test nested function calls
set nested_result = add(factorial(4), power(2.0, 3))
if nested_result > 20.0:
    say "Nested function test: PASSED (" + nested_result + ")"
    set passed_tests = passed_tests + 1
else:
    say "Nested function test: FAILED (" + nested_result + ")"
end

# Test complex expressions
if complex_calc > 1000.0:
    say "Complex expression test: PASSED (" + complex_calc + ")"
    set passed_tests = passed_tests + 1
else:
    say "Complex expression test: FAILED (" + complex_calc + ")"
end

# ===== PERFORMANCE TESTS =====

# Large calculations
set large_factorial = factorial(10)
set large_power = power(3.0, 15)
set performance_result = large_factorial + large_power

# Multiple operations
set perf_test_1 = PI * E * GOLDEN_RATIO
set perf_test_2 = factorial(8) / power(2.0, 5)
set perf_test_3 = (width * height) / (aspect_ratio * 1000.0)

# ===== FINAL RESULTS =====

say "================================================="
say program_name + " v" + version
say "================================================="

say ""
say "Mathematical Constants:"
say "PI = " + PI
say "E = " + E
say "Golden Ratio = " + GOLDEN_RATIO

say ""
say "Calculation Results:"
say "5! = " + fact_5
say "7! = " + fact_7
say "10! = " + large_factorial
say "2^10 = " + power_result
say "3^15 = " + large_power

say ""
say "Geometric Calculations:"
say "Circle area (r=5) = " + circle_area
say "Sphere volume (r=3) = " + sphere_volume

say ""
say "Complex Results:"
say "Sum result = " + sum_result
say "Product result = " + product_result
say "Complex calculation = " + complex_calc
say "Nested result = " + nested_result
say "Performance result = " + performance_result

say ""
say "Performance Tests:"
say "Perf test 1 = " + perf_test_1
say "Perf test 2 = " + perf_test_2
say "Perf test 3 = " + perf_test_3

say ""
say "Test Summary:"
say "Total tests: " + test_count
say "Passed tests: " + passed_tests
say "Failed tests: " + (test_count - passed_tests)

if passed_tests == test_count:
    say ""
    say "*** ALL TESTS PASSED! ***"
    say "Dolet compiler is working perfectly!"
else:
    say ""
    say "*** SOME TESTS FAILED ***"
    say "Check results above for details."
end

say "================================================="

# Working Comprehensive Test Suite for Dolet Compiler
# Testing all implemented features together with complex algorithms

# ===== VARIABLE DECLARATIONS & TYPE INFERENCE =====
set program_name = "Dolet Comprehensive Test"
set version = 1.0
set test_count = 0
set passed_tests = 0
set failed_tests = 0

# Test different numeric types
set small_int = 42
set large_int = 999999
set negative_int = -123
set simple_float = 3.14
set precise_float = 2.71828
set very_precise_double = 3.141592653589793
set scientific_notation = 1.23456789

# Test boolean and null
set is_testing = true
set is_complete = false
set optional_data = null

# ===== EXPLICIT TYPE DECLARATIONS =====
set width: int = 1920
set height: int = 1080
set aspect_ratio: float = 1.777
set precision: double = 0.000000001
set title: string = "Test Window"
set debug_mode: bool = true

# ===== CONSTANTS =====
const PI = 3.141592653589793
const E = 2.718281828459045
const GOLDEN_RATIO = 1.618033988749895
const MAX_ITERATIONS = 1000
const EPSILON = 0.0001

# ===== COMPLEX MATHEMATICAL FUNCTIONS =====

# Factorial calculation function
fun factorial(n: int) -> int:
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)
    end
end

# Fibonacci sequence function
fun fibonacci(n: int) -> int:
    if n <= 1:
        return n
    else:
        return fibonacci(n - 1) + fibonacci(n - 2)
    end
end

# Power function implementation
fun power(base: float, exponent: int) -> float:
    if exponent == 0:
        return 1.0
    else:
        if exponent > 0:
            return base * power(base, exponent - 1)
        else:
            return 1.0 / power(base, -exponent)
        end
    end
end

# Greatest Common Divisor (Euclidean algorithm)
fun gcd(a: int, b: int) -> int:
    if b == 0:
        return a
    else:
        return gcd(b, a % b)
    end
end

# ===== ARRAY OPERATIONS =====
set numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
set primes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29]
set fibonacci_sequence = [0, 1, 1, 2, 3, 5, 8, 13, 21, 34]
set test_scores = [95.5, 87.2, 92.8, 78.9, 88.1, 94.3, 85.7, 91.2]

# String arrays
set programming_languages = ["Rust", "C++", "Python", "JavaScript", "Go"]
set test_messages = ["Starting tests", "Running algorithms", "Checking performance", "Tests complete"]

# ===== COMPLEX EXPRESSIONS & CALCULATIONS =====

# Mathematical calculations
set circle_area = PI * power(5.0, 2)
set sphere_volume = (4.0 / 3.0) * PI * power(3.0, 3)
set compound_interest = 1000.0 * power(1.05, 10)

# Factorial calculations
set fact_5 = factorial(5)
set fact_10 = factorial(10)

# Fibonacci calculations
set fib_10 = fibonacci(10)
set fib_15 = fibonacci(15)

# GCD calculations
set gcd_48_18 = gcd(48, 18)
set gcd_100_75 = gcd(100, 75)

# Complex nested expressions
set complex_calc = (fact_5 + fib_10) * (circle_area / PI) + power(E, 2)
set ratio_test = (GOLDEN_RATIO * PI) / (E + 1.0)

# ===== CONDITIONAL LOGIC TESTING =====

# Test basic conditions
if version >= 1.0:
    say "Version check passed"
    set passed_tests = passed_tests + 1
else:
    say "Version check failed"
    set failed_tests = failed_tests + 1
end

# Test complex conditions
if fact_5 == 120:
    say "Factorial test passed: 5! = " + fact_5
    set passed_tests = passed_tests + 1
else:
    say "Factorial test failed: 5! = " + fact_5
    set failed_tests = failed_tests + 1
end

# Test floating point comparisons
if circle_area > 75.0:
    say "Circle area calculation passed: " + circle_area
    set passed_tests = passed_tests + 1
else:
    say "Circle area calculation failed: " + circle_area
    set failed_tests = failed_tests + 1
end

# Test boolean logic
if is_testing == true:
    if debug_mode == true:
        say "Debug mode is active"
        set passed_tests = passed_tests + 1
    else:
        say "Debug mode is inactive"
        set failed_tests = failed_tests + 1
    end
else:
    say "Testing mode is disabled"
    set failed_tests = failed_tests + 1
end

# ===== FUNCTION CALL CHAINS =====

# Nested function calls
set nested_result = power(factorial(4), 2)
set chain_result = gcd(factorial(6), fibonacci(12))

# Function calls with expressions
set expression_result = power(PI, 2) + power(E, 2)
set combined_result = factorial(gcd(12, 8)) + fibonacci(7)

# ===== PERFORMANCE STRESS TESTS =====

# Large number calculations
set large_factorial = factorial(12)
set large_fibonacci = fibonacci(18)
set large_power = power(2.0, 20)

# Multiple complex calculations
set stress_test_1 = power(PI, 3) * factorial(7) / GOLDEN_RATIO
set stress_test_2 = fibonacci(16) + power(E, 3) - factorial(6)
set stress_test_3 = gcd(factorial(8), fibonacci(14)) * PI

# ===== OUTPUT AND RESULTS =====

say "========================================="
say program_name + " v" + version
say "========================================="

say "Mathematical Constants:"
say "PI = " + PI
say "E = " + E
say "Golden Ratio = " + GOLDEN_RATIO

say ""
say "Factorial Results:"
say "5! = " + fact_5
say "10! = " + fact_10
say "12! = " + large_factorial

say ""
say "Fibonacci Results:"
say "fib(10) = " + fib_10
say "fib(15) = " + fib_15
say "fib(18) = " + large_fibonacci

say ""
say "GCD Results:"
say "gcd(48, 18) = " + gcd_48_18
say "gcd(100, 75) = " + gcd_100_75

say ""
say "Geometric Calculations:"
say "Circle area (r=5) = " + circle_area
say "Sphere volume (r=3) = " + sphere_volume
say "Compound interest = " + compound_interest

say ""
say "Complex Calculations:"
say "Nested result = " + nested_result
say "Chain result = " + chain_result
say "Expression result = " + expression_result
say "Combined result = " + combined_result

say ""
say "Stress Test Results:"
say "Stress test 1 = " + stress_test_1
say "Stress test 2 = " + stress_test_2
say "Stress test 3 = " + stress_test_3

say ""
say "Test Summary:"
say "Total tests: " + (passed_tests + failed_tests)
say "Passed: " + passed_tests
say "Failed: " + failed_tests

if failed_tests == 0:
    say "ALL TESTS PASSED!"
else:
    say "Some tests failed. Check results above."
end

say "========================================="

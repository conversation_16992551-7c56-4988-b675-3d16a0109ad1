# Test Array Indexing - Check if it works
say "Testing Array Indexing..."

# Test 1: Basic array creation
set numbers = [10, 20, 30, 40, 50]
say "Array created: [10, 20, 30, 40, 50]"

# Test 2: Array indexing
say "Test 2: Array indexing"
set first = numbers[0]
say "First element: " + first

set second = numbers[1]
say "Second element: " + second

set last = numbers[4]
say "Last element: " + last

# Test 3: Array modification
say "Test 3: Array modification"
set numbers[0] = 100
say "Modified first element to 100"
say "New first element: " + numbers[0]

# Test 4: String array indexing
set names = ["<PERSON>", "<PERSON>", "<PERSON>"]
say "String array created"
set first_name = names[0]
say "First name: " + first_name

say "Array indexing test complete!"

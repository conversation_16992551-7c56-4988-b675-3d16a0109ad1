use std::io::{self, Write};


fn main() {
    println!("{}", "=========================================");
    println!("{}", "String Escaping and Character Literals Test");
    println!("{}", "=========================================");
    let mut escaped_quote = "She said \\"Hello World\\"";
    let mut escaped_backslash = "Path: C:\\Users\\<USER>\nLine 2";
    let mut escaped_tab = "Column1\tColumn2";
    println!("{}", "String Escaping Tests:");
    println!("{}", escaped_quote);
    println!("{}", escaped_backslash);
    println!("{}", escaped_newline);
    println!("{}", escaped_tab);
    println!("{}", "");
    let mut char_a = /* unsupported expr: Char('A') */;
    let mut char_digit = /* unsupported expr: Char('5') */;
    let mut char_symbol = /* unsupported expr: Char('@') */;
    let mut char_space = /* unsupported expr: Char(' ') */;
    println!("{}", "Character Literal Tests:");
    println!("{}", format!("{}{}" , "char_a = ", char_a));
    println!("{}", format!("{}{}" , "char_digit = ", char_digit));
    println!("{}", format!("{}{}" , "char_symbol = ", char_symbol));
    println!("{}", format!("{}{}" , format!("{}{}" , "char_space = '", char_space), "'"));
    println!("{}", "");
    let mut char_newline = /* unsupported expr: Char('\\') */;
    let mut char_tab = /* unsupported expr: Char('\\') */;
    let mut char_backslash = /* unsupported expr: Char('\\') */;
    let mut char_quote = /* unsupported expr: Char('\\') */;
    println!("{}", "Escaped Character Literals:");
    println!("{}", format!("{}{}" , "char_newline = ", char_newline));
    println!("{}", format!("{}{}" , "char_tab = ", char_tab));
    println!("{}", format!("{}{}" , "char_backslash = ", char_backslash));
    println!("{}", format!("{}{}" , "char_quote = ", char_quote));
    println!("{}", "");
    println!("{}", "Mixed Operations:");
    println!("{}", format!("{}{}" , format!("{}{}" , "Character in string: ", char_a), " is a letter"));
    println!("{}", (format!("{}{}" , format!("{}{}" , "Multiple chars: ", char_a), char_digit) + char_symbol));
    println!("{}", format!("{}{}" , format!("{}{}" , "Escaped quote in context: ", escaped_quote), " - complete"));
    println!("{}", "");
    let mut complex_string = "Text with \\"quotes\\" and \\backslashes\\ and \ttabs\t";
    println!("{}", "Complex Escaping:");
    println!("{}", complex_string);
    println!("{}", "");
    let mut all_escapes = "Newline:\nTab:\tQuote:\\"Backslash:\\Null:\0End";
    println!("{}", "All Escape Sequences:");
    println!("{}", all_escapes);
    println!("{}", "=========================================");
    println!("{}", "String Escaping Test Complete");
    println!("{}", "=========================================");
}

use std::io::{self, Write};


fn main() {
    println!("{}", "Testing Built-in Functions...");
    println!("{}", "Test 1: Math functions");
    let mut sqrt_result = (16 as f64).sqrt();
    println!("{}", format!("{}{}" , "sqrt(16) = ", sqrt_result));
    let mut power_result = (2 as f64).powf(3 as f64);
    println!("{}", format!("{}{}" , "power(2, 3) = ", power_result));
    let mut abs_result = ((-5) as f64).abs();
    println!("{}", format!("{}{}" , "abs(-5) = ", abs_result));
    println!("{}", "Test 2: String functions");
    let mut str_length = "hello".len();
    println!("{}", format!("{}{}" , "length('hello') = ", str_length));
    let mut uppercase = "hello".to_uppercase();
    println!("{}", format!("{}{}" , "upper('hello') = ", uppercase));
    let mut lowercase = "WORLD".to_lowercase();
    println!("{}", format!("{}{}" , "lower('WORLD') = ", lowercase));
    println!("{}", "Test 3: Array functions");
    let mut numbers = vec![1, 2, 3, 4, 5];
    let mut array_length = numbers.len();
    println!("{}", format!("{}{}" , "length([1,2,3,4,5]) = ", array_length));
    let mut array_sum = numbers.iter().sum::<i64>();
    println!("{}", format!("{}{}" , "sum([1,2,3,4,5]) = ", array_sum));
    println!("{}", "Test 4: Type conversion");
    let mut str_to_int = "123".parse::<i64>().unwrap_or(0);
    println!("{}", format!("{}{}" , "int('123') = ", str_to_int));
    let mut int_to_str = 456.to_string();
    println!("{}", format!("{}{}" , "string(456) = ", int_to_str));
    let mut str_to_float = "3.14".parse::<f64>().unwrap_or(0.0);
    println!("{}", format!("{}{}" , "float('3.14') = ", str_to_float));
    println!("{}", "Built-in functions test complete!");
}

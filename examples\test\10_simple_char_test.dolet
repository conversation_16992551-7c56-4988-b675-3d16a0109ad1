# Simple Character Test
# Testing basic character literal support

say "========================================="
say "Simple Character Test"
say "========================================="

# Basic character literals
set char_a = 'A'
set char_digit = '5'

say "Basic Character Literals:"
say "char_a = " + char_a
say "char_digit = " + char_digit

say "========================================="
say "Simple Character Test Complete"
say "========================================="

use std::io::{self, Write};


fn main() {
    println!("{}", "=========================================");
    println!("{}", "Arrays Test");
    println!("{}", "=========================================");
    let mut numbers = vec![1, 2, 3, 4, 5];
    let mut strings = vec!["hello", "world", "test"];
    let mut booleans = vec![true, false, true];
    let mut mixed = (1, "hello", true, 3.14f32,);
    println!("{}", "Basic Arrays:");
    println!("{}", format!("{}{}" , "numbers = ", format!("{:?}", numbers)));
    println!("{}", format!("{}{}" , "strings = ", strings));
    println!("{}", format!("{}{}" , "booleans = ", booleans));
    println!("{}", format!("{}{}" , "mixed = ", mixed));
    println!("{}", "");
    let mut empty_array = vec![0i64; 0];
    println!("{}", "Empty Array:");
    println!("{}", format!("{}{}" , "empty_array = ", empty_array));
    println!("{}", "");
    println!("{}", "Array Access:");
    println!("{}", format!("{}{}" , "numbers[0] = ", numbers[0 as usize]));
    println!("{}", format!("{}{}" , "numbers[1] = ", numbers[1 as usize]));
    println!("{}", format!("{}{}" , "strings[0] = ", strings[0 as usize]));
    println!("{}", format!("{}{}" , "strings[1] = ", strings[1 as usize]));
    println!("{}", "");
    println!("{}", "Array Length:");
    println!("{}", format!("{}{}" , "length of numbers = ", len(numbers)));
    println!("{}", format!("{}{}" , "length of strings = ", len(strings)));
    println!("{}", format!("{}{}" , "length of empty_array = ", len(empty_array)));
    println!("{}", "");
    println!("{}", "Array Operations:");
    let mut first_num = numbers[0 as usize];
    let mut second_num = numbers[1 as usize];
    let mut sum = (first_num + second_num);
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "First two numbers sum: ", first_num), " + "), second_num), " = "), sum));
    println!("{}", "");
    let mut nested = vec![vec![1, 2], vec![3, 4], vec![5, 6]];
    println!("{}", "Nested Arrays:");
    println!("{}", format!("{}{}" , "nested = ", nested));
    println!("{}", format!("{}{}" , "nested[0] = ", nested[0 as usize]));
    println!("{}", format!("{}{}" , "nested[0][0] = ", nested[0 as usize][0 as usize]));
    println!("{}", "=========================================");
    println!("{}", "Arrays Test Complete");
    println!("{}", "=========================================");
}

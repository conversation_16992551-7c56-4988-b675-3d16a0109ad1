use crate::token::DoletType;

#[cfg(feature = "parallel")]
use hashbrown::HashMap;

#[cfg(not(feature = "parallel"))]
use std::collections::HashMap;
use std::collections::VecDeque;

/// High-performance symbol table for Dolet compiler
/// Features:
/// - Fast HashMap-based lookups
/// - Scoped variable tracking
/// - Type safety enforcement
/// - Constant vs variable distinction
#[derive(Debug, <PERSON>lone)]
pub struct SymbolTable {
    scopes: VecDeque<Scope>,
    global_scope: Scope,
}

#[derive(Debug, <PERSON>lone)]
struct Scope {
    symbols: HashMap<String, Symbol>,
    parent: Option<usize>,
}

#[derive(Debug, <PERSON><PERSON>)]
pub struct Symbol {
    pub name: String,
    pub symbol_type: DoletType,
    pub is_const: bool,
    pub is_initialized: bool,
    pub line: usize,
    pub column: usize,
}

#[derive(Debug, <PERSON>lone)]
pub enum SymbolError {
    UndefinedVariable {
        name: String,
        line: usize,
        column: usize,
    },
    RedefinedVariable {
        name: String,
        original_line: usize,
        redefined_line: usize,
    },
    TypeMismatch {
        name: String,
        expected: DoletType,
        found: DoletType,
        line: usize,
        column: usize,
    },
    ConstantReassignment {
        name: String,
        line: usize,
        column: usize,
    },
    UninitializedVariable {
        name: String,
        line: usize,
        column: usize,
    },
}

impl SymbolTable {
    pub fn new() -> Self {
        Self {
            scopes: VecDeque::new(),
            global_scope: Scope {
                symbols: HashMap::new(),
                parent: None,
            },
        }
    }

    /// Enter a new scope (for functions, blocks, etc.)
    pub fn enter_scope(&mut self) {
        let new_scope = Scope {
            symbols: HashMap::new(),
            parent: if self.scopes.is_empty() { None } else { Some(self.scopes.len() - 1) },
        };
        self.scopes.push_back(new_scope);
    }

    /// Exit the current scope
    pub fn exit_scope(&mut self) {
        self.scopes.pop_back();
    }

    /// Define a new symbol in the current scope
    pub fn define(&mut self, symbol: Symbol) -> Result<(), SymbolError> {
        let current_scope = if self.scopes.is_empty() {
            &mut self.global_scope
        } else {
            self.scopes.back_mut().unwrap()
        };

        // Check for redefinition in current scope
        if current_scope.symbols.contains_key(&symbol.name) {
            let existing = &current_scope.symbols[&symbol.name];
            return Err(SymbolError::RedefinedVariable {
                name: symbol.name,
                original_line: existing.line,
                redefined_line: symbol.line,
            });
        }

        current_scope.symbols.insert(symbol.name.clone(), symbol);
        Ok(())
    }

    /// Look up a symbol by name (searches all scopes)
    pub fn lookup(&self, name: &str) -> Option<&Symbol> {
        // Search current scopes from innermost to outermost
        for scope in self.scopes.iter().rev() {
            if let Some(symbol) = scope.symbols.get(name) {
                return Some(symbol);
            }
        }

        // Search global scope
        self.global_scope.symbols.get(name)
    }

    /// Get a mutable reference to a symbol for updates
    pub fn get_mut(&mut self, name: &str) -> Option<&mut Symbol> {
        // Search current scopes from innermost to outermost
        for scope in self.scopes.iter_mut().rev() {
            if scope.symbols.contains_key(name) {
                return scope.symbols.get_mut(name);
            }
        }

        // Search global scope
        self.global_scope.symbols.get_mut(name)
    }

    /// Check if a variable is defined
    pub fn is_defined(&self, name: &str) -> bool {
        self.lookup(name).is_some()
    }

    /// Assign a value to an existing variable with type checking
    pub fn assign(&mut self, name: &str, new_type: DoletType, line: usize, column: usize) -> Result<(), SymbolError> {
        // First, check if the symbol exists and get its current type
        let (is_const, current_type) = {
            let symbol = self.lookup(name).ok_or_else(|| SymbolError::UndefinedVariable {
                name: name.to_string(),
                line,
                column,
            })?;
            (symbol.is_const, symbol.symbol_type.clone())
        };

        // Check if it's a constant
        if is_const {
            return Err(SymbolError::ConstantReassignment {
                name: name.to_string(),
                line,
                column,
            });
        }

        // Type checking with nullable support
        if !self.types_compatible(&current_type, &new_type) {
            return Err(SymbolError::TypeMismatch {
                name: name.to_string(),
                expected: current_type,
                found: new_type,
                line,
                column,
            });
        }

        // Now get mutable reference and update
        let symbol = self.get_mut(name).unwrap(); // Safe because we checked existence above

        // Update type if it was unknown or if assigning null to make it nullable
        if symbol.symbol_type == DoletType::Unknown {
            symbol.symbol_type = new_type;
        } else if new_type == DoletType::Null && !symbol.symbol_type.is_nullable() {
            symbol.symbol_type = symbol.symbol_type.clone().make_nullable();
        }

        symbol.is_initialized = true;
        Ok(())
    }

    /// Check if two types are compatible for assignment
    fn types_compatible(&self, expected: &DoletType, actual: &DoletType) -> bool {
        // Exact match
        if expected == actual {
            return true;
        }

        // Null can be assigned to any nullable type
        if *actual == DoletType::Null {
            return true; // We'll make the type nullable during assignment
        }

        // Unknown type can accept any type (for type inference)
        if *expected == DoletType::Unknown {
            return true;
        }

        // Nullable type compatibility
        if let DoletType::Nullable(inner_expected) = expected {
            return self.types_compatible(inner_expected, actual) || *actual == DoletType::Null;
        }

        // Numeric type promotions (int -> float -> double)
        match (expected, actual) {
            (DoletType::Float, DoletType::Int) => true,
            (DoletType::Double, DoletType::Int) => true,
            (DoletType::Double, DoletType::Float) => true,
            _ => false,
        }
    }

    /// Get all symbols in the current scope (for debugging)
    pub fn current_scope_symbols(&self) -> Vec<&Symbol> {
        let current_scope = if self.scopes.is_empty() {
            &self.global_scope
        } else {
            self.scopes.back().unwrap()
        };

        current_scope.symbols.values().collect()
    }

    /// Get the current scope depth
    pub fn scope_depth(&self) -> usize {
        self.scopes.len()
    }
}

impl Symbol {
    pub fn new(
        name: String,
        symbol_type: DoletType,
        is_const: bool,
        line: usize,
        column: usize,
    ) -> Self {
        Self {
            name,
            symbol_type,
            is_const,
            is_initialized: false,
            line,
            column,
        }
    }

    pub fn with_initialization(
        name: String,
        symbol_type: DoletType,
        is_const: bool,
        line: usize,
        column: usize,
    ) -> Self {
        Self {
            name,
            symbol_type,
            is_const,
            is_initialized: true,
            line,
            column,
        }
    }
}

impl std::fmt::Display for SymbolError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SymbolError::UndefinedVariable { name, line, column } => {
                write!(f, "Undefined variable '{}' at line {}, column {}", name, line, column)
            }
            SymbolError::RedefinedVariable { name, original_line, redefined_line } => {
                write!(f, "Variable '{}' redefined at line {} (originally defined at line {})", 
                       name, redefined_line, original_line)
            }
            SymbolError::TypeMismatch { name, expected, found, line, column } => {
                write!(f, "Type mismatch for variable '{}' at line {}, column {}: expected {:?}, found {:?}", 
                       name, line, column, expected, found)
            }
            SymbolError::ConstantReassignment { name, line, column } => {
                write!(f, "Cannot reassign constant '{}' at line {}, column {}", name, line, column)
            }
            SymbolError::UninitializedVariable { name, line, column } => {
                write!(f, "Use of uninitialized variable '{}' at line {}, column {}", name, line, column)
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_symbol_definition_and_lookup() {
        let mut table = SymbolTable::new();
        
        let symbol = Symbol::new("x".to_string(), DoletType::Int, false, 1, 1);
        table.define(symbol).unwrap();
        
        assert!(table.is_defined("x"));
        let found = table.lookup("x").unwrap();
        assert_eq!(found.name, "x");
        assert_eq!(found.symbol_type, DoletType::Int);
    }

    #[test]
    fn test_scoping() {
        let mut table = SymbolTable::new();
        
        // Define in global scope
        let global_symbol = Symbol::new("global".to_string(), DoletType::Int, false, 1, 1);
        table.define(global_symbol).unwrap();
        
        // Enter new scope
        table.enter_scope();
        let local_symbol = Symbol::new("local".to_string(), DoletType::String, false, 2, 1);
        table.define(local_symbol).unwrap();
        
        // Both should be visible
        assert!(table.is_defined("global"));
        assert!(table.is_defined("local"));
        
        // Exit scope
        table.exit_scope();
        
        // Only global should be visible
        assert!(table.is_defined("global"));
        assert!(!table.is_defined("local"));
    }

    #[test]
    fn test_type_compatibility() {
        let table = SymbolTable::new();
        
        // Exact matches
        assert!(table.types_compatible(&DoletType::Int, &DoletType::Int));
        
        // Null compatibility
        assert!(table.types_compatible(&DoletType::Int, &DoletType::Null));
        
        // Numeric promotions
        assert!(table.types_compatible(&DoletType::Float, &DoletType::Int));
        assert!(table.types_compatible(&DoletType::Double, &DoletType::Float));
        
        // Incompatible types
        assert!(!table.types_compatible(&DoletType::String, &DoletType::Int));
    }
}
